<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://www.incert.at
 * @since             1.0.0
 * @package           Incert_Voucher_Handling
 *
 * @wordpress-plugin
 * Plugin Name:       incert Voucher Management System
 * Plugin URI:        https://support.incert.at/hc/de/articles/21818233089309-WOOCOMMERCE-Einl%C3%B6sung-und-Verkauf-im-Webshop
 * Description:       Enables incert Voucher Management System (https://www.incert.at) to be used with WooCommerce, within voucher selling and voucher redemption in woocommerce.
 * Version:           1.0.3
 * Author:            Incert eTourismus GmbH & Co KG
 * Author URI:        https://www.incert.at
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       incert-coupons
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.0 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define( 'INCERT_COUPONS_VERSION', '1.0.3' );

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-incert-coupons-activator.php
 */
function IC_activate_incert_coupons() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-incert-coupons-activator.php';
	Incert_Coupons_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-incert-coupons-deactivator.php
 */
function IC_deactivate_incert_coupons() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-incert-coupons-deactivator.php';
	Incert_Coupons_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'IC_activate_incert_coupons' );
register_deactivation_hook( __FILE__, 'IC_deactivate_incert_coupons' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-incert-coupons.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */
function IC_run_incert_coupons() {
	$plugin = new Incert_Coupons();
	$plugin->run();
}

IC_run_incert_coupons();
