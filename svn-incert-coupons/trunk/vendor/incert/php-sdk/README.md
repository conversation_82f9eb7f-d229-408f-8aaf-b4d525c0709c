# Client

Client SDK for Incert Coupon usage

## Installation & Usage

### Requirements

PHP 7.4 and later.

### Composer

To install the bindings via [Composer](https://getcomposer.org/), add the following to `composer.json`:

```json
{
  "repositories": [
    {
      "type": "vcs",
      "url": "https://github.com/incert/php-sdk.git"
    }
  ],
  "require": {
    "incert/php-sdk": "^2.0"
  }
}
```

Then run `composer install`

### Manual Installation

Download the files and include `autoload.php`:

```php
<?php
require_once('/path/to/Client/vendor/autoload.php');
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure API key authorization: authenticateKey
$config = new Incert\Client\Configuration();
$config->setApiKey('X-Api-Key', 'YOUR_API_KEY');

// Set hostname for your API endpoint
// Default Host: https://api-sandbox.myincert.com/api
$config->setHost('YOUR_API_ENDPOINT');


$apiInstance = new Incert\Client\Api\OAuth20ClientCredentialsApi($config);

$accessToken = new \Incert\Client\OAuth20\GrantAccessTokenRequest();
$accessToken->setClientId('YOUR_CLIENT_ID')->setClientSecret('YOUR_CLIENT_SECRET');

try {
    $result = $apiInstance->oauthAccessTokenPost($accessToken);
    $token = $result->getAccessToken();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling OAuth20ClientCredentialsApi->oauthAccessTokenPost: ', $e->getMessage(), PHP_EOL;
}

```

## API Endpoints

All URIs are relative to Incert API Endpoint

- 

## Authorization

Authentication schemes defined for the API:
### authenticateKey

- **Type**: API key
- **API key parameter name**: X-Api-Key
- **Location**: HTTP header


### authenticateBearer

- **Type**: Bearer authentication

### authenticateSession

- **Type**: API key
- **API key parameter name**: XTCsid
- **Location**: 


## Tests

To run the tests, use:

```bash
composer install
vendor/bin/phpunit
```