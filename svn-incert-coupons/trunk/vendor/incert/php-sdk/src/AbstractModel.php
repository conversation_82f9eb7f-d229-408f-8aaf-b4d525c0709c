<?php

namespace Incert\Client;

class AbstractModel  implements \ArrayAccess, \JsonSerializable
{

    protected $data = [];

    public function __construct($data = [])
    {
        foreach ($this->data as $key => $defaultValue) {
            $this->setDataUsingMethod($key, $data[$key] ?? $defaultValue );
        }
    }

    public function setDataUsingMethod($key, $value)
    {
        $method = 'set' . ($key !== null ? str_replace('_', '', ucwords($key, '_')) : '');
        if (method_exists($this, $method)) {
            $this->{$method}($value);
        }
        else {
            $this->data[$key] = $value;
        }
        return $this;
    }

    /**
     * Specify data which should be serialized to JSON
     */
    public function jsonSerialize(): mixed
    {
        return $this->data;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists(mixed $offset): bool
    {
        return isset($this->data[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet(mixed $offset): mixed
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->data[] = $value;
        } else {
            $this->data[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->data[$offset]);
    }

    /**
     * @param $data
     * @return $this
     */
    public function setData($data)
    {
        foreach ($data as $key => $value) {
            if(isset($this->data[$key])){
                $this->data[$key] = $value;
            }
        }
        return $this;
    }

    /**
     * @return array|mixed
     */
    public function getData()
    {
        return $this->data;
    }
}