<?php

namespace Incert\Client\Model\Country;

use Incert\Client\AbstractModel;

class ExistsRequest extends AbstractModel
{

    protected $data = [
      'id' => null,  
      'isoCode2' => null,  
      'isoCode3' => null,  
    ];

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->data['id'];
    }

    /**
     * @param mixed $id
     * @return ExistsRequest
     */
    public function setId($id)
    {
        $this->data['id'] = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsoCode2()
    {
        return $this->data['isoCode2'];
    }

    /**
     * @param mixed $isoCode2
     * @return ExistsRequest
     */
    public function setIsoCode2($isoCode2)
    {
        $this->data['isoCode2'] = $isoCode2;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsoCode3()
    {
        return $this->data['isoCode3'];
    }

    /**
     * @param mixed $isoCode3
     * @return ExistsRequest
     */
    public function setIsoCode3($isoCode3)
    {
        $this->data['isoCode3'] = $isoCode3;
        return $this;
    }


}