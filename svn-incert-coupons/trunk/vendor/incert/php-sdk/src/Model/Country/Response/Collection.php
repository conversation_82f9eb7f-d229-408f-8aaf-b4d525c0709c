<?php

namespace Incert\Client\Model\Country\Response;

use Incert\Client\AbstractModel;
use Incert\Client\Model\Country\Response;

class Collection extends AbstractModel
{
    protected $data = [];

    public function __construct($data = [])
    {
        foreach ($data as $country) {
            $countryModel = new Response($country);
            $this->data[$countryModel->getId()] = $countryModel;
        }
    }
}