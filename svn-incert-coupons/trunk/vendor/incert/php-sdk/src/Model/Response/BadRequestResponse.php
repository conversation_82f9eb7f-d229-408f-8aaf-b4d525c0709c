<?php
/**
 * IncertCommonApiModelBadRequestResponse
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Response;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelBadRequestResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class BadRequestResponse extends AbstractModel
{

    protected $data = [
        'error'   => "",
        'message' => "",
        'code'    => "",
    ];

    /**
     * @return mixed|string
     */
    public function getError()
    {
        return $this->data['error'];
    }

    /**
     * @param mixed|string $error
     */
    public function setError($error)
    {
        $this->data['error'] = $error;
        return $this;
    }

    /**
     * @return mixed|string
     */
    public function getMessage()
    {
        return $this->data['message'];
    }

    /**
     * @param $message
     * @return void
     */
    public function setMessage($message)
    {
        $this->data['message'] = $message;
        return $this;
    }

    public function getCode()
    {
        return $this->data['code'];
    }

    public function setCode($code)
    {
        $this->data['code'] = $code;
        return $this;
    }

}


