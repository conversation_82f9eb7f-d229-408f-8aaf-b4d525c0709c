<?php
/**
 * Order
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request
 */

namespace Incert\Client\Model\Shop\Request;

use Incert\Client\AbstractModel;
use Incert\Client\Model\Shop\Request\Order\Customer;

/**
 * Order Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request
 * @implements \ArrayAccess<string, mixed>
 */
class Order extends AbstractModel
{
    protected $data = [
        'shippingModule' => null,
        'paymentModule' => null,
        'sendInvoice' => null,
        'sendInvoiceAdminCopy' => null,
        'customer' => [],
        'shoppingCart' => [],
    ];

    public function getShippingModule(): ?string
    {
        return $this->data['shippingModule'];
    }

    public function setShippingModule(?string $shippingModule): self
    {
        $this->data['shippingModule'] = $shippingModule;
        return $this;
    }

    public function getPaymentModule(): ?string
    {
        return $this->data['paymentModule'];
    }

    public function setPaymentModule(?string $paymentModule): self
    {
        $this->data['paymentModule'] = $paymentModule;
        return $this;
    }

    public function getSendInvoice(): ?bool
    {
        return $this->data['sendInvoice'];
    }

    public function setSendInvoice(?bool $sendInvoice): self
    {
        $this->data['sendInvoice'] = $sendInvoice;
        return $this;
    }

    public function getSendInvoiceAdminCopy(): ?bool
    {
        return $this->data['sendInvoiceAdminCopy'];
    }

    public function setSendInvoiceAdminCopy(?bool $sendInvoiceAdminCopy): self
    {
        $this->data['sendInvoiceAdminCopy'] = $sendInvoiceAdminCopy;
        return $this;
    }

    public function getCustomer(): array
    {
        return $this->data['customer'];
    }

    public function setCustomer(array $customer): self
    {
        $this->data['customer'] = new Customer($customer);
        return $this;
    }

    public function getShoppingCart(): array
    {
        return $this->data['shoppingCart'];
    }

    public function setShoppingCart(array $shoppingCart): self
    {
        $this->data['shoppingCart'] = $shoppingCart;
        $this->data['shoppingCart']['products'] = [];
        foreach ($shoppingCart['products'] ?? [] as $product) {
            $this->data['shoppingCart']['products'][] = new Order\ShoppingCart\Product($product);
        }
        return $this;
    }
}
