<?php
/**
 * Cancel
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Product
 */

namespace Incert\Client\Model\Shop\Request\Product;

use Incert\Client\AbstractModel;

/**
 * Cancel Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Product
 * @implements \ArrayAccess<string, mixed>
 */
class Cancel extends AbstractModel
{
    protected $data = [
        'comment' => "",
        'itemId' => "",
        'qty' => 0,
    ];

    public function getComment(): string
    {
        return $this->data['comment'];
    }

    public function setComment($comment)
    {
        $this->data['comment'] = $comment;
        return $this;
    }

    public function getItemId(): string
    {
        return $this->data['itemId'];
    }

    public function setItemId($itemId)
    {
        $this->data['itemId'] = $itemId;
        return $this;
    }

    public function getQty(): string
    {
        return $this->data['qty'];
    }

    public function setQty($qty)
    {
        $this->data['qty'] = $qty;
        return $this;
    }
}
