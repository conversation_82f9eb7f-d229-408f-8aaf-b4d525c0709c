<?php
/**
 * Order
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response
 */

namespace Incert\Client\Model\Shop\Response;

use Incert\Client\AbstractModel;

/**
 * Order Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response
 * @implements \ArrayAccess<string, mixed>
 */
class Order extends AbstractModel
{
    protected $data = [
        'orderId' => null,
        'customerId' => null,
        'orderedVouchers' => [],
    ];

    public function getOrderId(): mixed
    {
        return $this->data['orderId'];
    }

    public function setOrderId(mixed $orderId)
    {
        $this->data['orderId'] = $orderId;
        return $this;
    }

    public function getCustomerId(): mixed
    {
        return $this->data['customerId'];
    }

    public function setCustomerId(mixed $customerId)
    {
        $this->data['customerId'] = $customerId;
        return $this;
    }

    /**
     * @return Order\Voucher[]
     */
    public function getOrderedVouchers(): array
    {
        return $this->data['orderedVouchers'];
    }

    public function setOrderedVouchers(array $orderedVouchers)
    {
        foreach ($orderedVouchers as $voucher) {
            $this->data['orderedVouchers'][] = new Order\Voucher($voucher);
        }
        return $this;
    }
}
