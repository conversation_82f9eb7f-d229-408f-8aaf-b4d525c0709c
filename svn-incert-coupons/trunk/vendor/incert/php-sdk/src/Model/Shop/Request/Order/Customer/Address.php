<?php
/**
 * Address
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order\Customer
 */

namespace Incert\Client\Model\Shop\Request\Order\Customer;

use Incert\Client\AbstractModel;

/**
 * Address Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order\Customer
 * @implements \ArrayAccess<string, mixed>
 */
class Address extends AbstractModel
{
    protected $data = [
        'title' => null,
        'firstName' => null,
        'lastName' => null,
        'gender' => null,
        'city' => null,
        'postalCode' => null,
        'street' => null,
        'company' => null,
        'country' => null,
    ];

    public function getTitle(): mixed
    {
        return $this->data['title'];
    }

    public function setTitle(mixed $title)
    {
        $this->data['title'] = $title;
        return $this;
    }

    public function getFirstName(): mixed
    {
        return $this->data['firstName'];
    }

    public function setFirstName(mixed $firstName)
    {
        $this->data['firstName'] = $firstName;
        return $this;
    }

    public function getLastName(): mixed
    {
        return $this->data['lastName'];
    }

    public function setLastName(mixed $lastName)
    {
        $this->data['lastName'] = $lastName;
        return $this;
    }

    public function getGender(): mixed
    {
        return $this->data['gender'];
    }

    public function setGender(mixed $gender)
    {
        $this->data['gender'] = $gender;
        return $this;
    }

    public function getCity(): mixed
    {
        return $this->data['city'];
    }

    public function setCity(mixed $city)
    {
        $this->data['city'] = $city;
        return $this;
    }

    public function getPostalCode(): mixed
    {
        return $this->data['postalCode'];
    }

    public function setPostalCode(mixed $postalCode)
    {
        $this->data['postalCode'] = $postalCode;
        return $this;
    }

    public function getStreet(): mixed
    {
        return $this->data['street'];
    }

    public function setStreet(mixed $street)
    {
        $this->data['street'] = $street;
        return $this;
    }

    public function getCompany(): mixed
    {
        return $this->data['company'];
    }

    public function setCompany(mixed $company)
    {
        $this->data['company'] = $company;
        return $this;
    }

    public function getCountry(): mixed
    {
        return $this->data['country'];
    }

    public function setCountry(mixed $country)
    {
        $this->data['country'] = $country;
        return $this;
    }
}
