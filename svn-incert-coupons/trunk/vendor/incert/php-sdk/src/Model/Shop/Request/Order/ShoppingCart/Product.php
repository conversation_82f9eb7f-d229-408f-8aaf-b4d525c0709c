<?php
/**
 * Product
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order\ShoppingCart
 */

namespace Incert\Client\Model\Shop\Request\Order\ShoppingCart;

use Incert\Client\AbstractModel;

/**
 * Product Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order\ShoppingCart
 * @implements \ArrayAccess<string, mixed>
 */
class Product extends AbstractModel
{
    protected $data = [
        'id' => null,
        'quantity' => null,
        'voucherValue' => null,
        'name' => null,
    ];

    public function getId(): mixed
    {
        return $this->data['id'];
    }

    public function setId(mixed $id)
    {
        $this->data['id'] = $id;
        return $this;
    }

    public function getQuantity(): mixed
    {
        return $this->data['quantity'];
    }

    public function setQuantity(mixed $quantity)
    {
        $this->data['quantity'] = $quantity;
        return $this;
    }

    public function getVoucherValue(): mixed
    {
        return $this->data['voucherValue'];
    }

    public function setVoucherValue(mixed $voucherValue)
    {
        $this->data['voucherValue'] = $voucherValue;
        return $this;
    }

    public function getName(): mixed
    {
        return $this->data['name'];
    }

    public function setName(mixed $name)
    {
        $this->data['name'] = $name;
        return $this;
    }
}
