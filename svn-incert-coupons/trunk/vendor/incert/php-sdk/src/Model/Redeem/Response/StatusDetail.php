<?php
/**
 * IncertCommonApiModelRedeemStatusDetailResponse
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem\Response;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemStatusDetailResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class StatusDetail extends AbstractModel
{
    // Status Codes
    const VOUCHER_NOT_FOUND = 0;
    const VALID = 1;
    const CANCELED = 2;
    const FULLY_REDEEMED = 3;
    const VOUCHER_TYPE_NOT_ALLOWED = 4;
    const VOUCHER_NOT_PAID_YET = 5;
    const VOUCHER_ALREADY_EXPIRED = 6;
    const VOUCHER_NOT_VALID_FOR_THIS_STATION = 7;
    const VOUCHER_NOT_ACTIVATED = 8;
    const SPONSORING = "SPONSORING"; // Status Code Valid but Value is 0
    const PARTLY_REDEEMABLE = "PARTLY_REDEEMABLE";



    const STATUS_MAP = [
        'de' => [
            self::VOUCHER_NOT_FOUND => 'Der Gutscheincode "%s" ist ungültig.',
            self::SPONSORING => 'Der Gutscheincode "%s" ist zur Onlineeinlösung nicht zulässig.',
            self::CANCELED => 'Der Gutscheincode "%s" ist storniert.',
            self::FULLY_REDEEMED => 'Der Gutscheincode "%s" ist bereits vollständig eingelöst.',
            self::VOUCHER_TYPE_NOT_ALLOWED => 'Der Gutscheincode "%s" ist zur Onlineeinlösung nicht zulässig.',
            self::VOUCHER_NOT_PAID_YET => 'Der Gutscheincode "%s" ist zur Onlineeinlösung nicht zulässig.',
            self::VOUCHER_ALREADY_EXPIRED => 'Der Gutscheincode "%s" ist nicht mehr gültig.',
            self::VOUCHER_NOT_VALID_FOR_THIS_STATION => 'Der Gutscheincode "%s" ist hier nicht zulässig. Bei Rückfragen bitte an den Verkäufer wenden.',
            self::VOUCHER_NOT_ACTIVATED => 'Der Gutscheincode "%s" ist nicht aktiviert.',
            self::PARTLY_REDEEMABLE => 'Der Gutscheincode "%s" ist nur volleinlösbar.',
        ],
        'en' => [
            self::VOUCHER_NOT_FOUND => 'Vouchercode "%s" not valid.',
            self::SPONSORING => 'Vouchercode "%s" can not be used online.',
            self::CANCELED => 'Vouchercode "%s" is canceled.',
            self::FULLY_REDEEMED => 'Vouchercode "%s" is already fully redeemed.',
            self::VOUCHER_TYPE_NOT_ALLOWED => 'Vouchercode "%s" can not be used online.',
            self::VOUCHER_NOT_PAID_YET => 'Vouchercode "%s" has not been paid yet.',
            self::VOUCHER_ALREADY_EXPIRED => 'Vouchercode "%s" is expired.',
            self::VOUCHER_NOT_VALID_FOR_THIS_STATION => 'Vouchercode "%s" not valid here. Please contact vendor for questions.',
            self::VOUCHER_NOT_ACTIVATED => 'Vouchercode "%s" not activated.',
            self::PARTLY_REDEEMABLE => 'Vouchercode "%s" can only be fully redeemed.',
        ],
    ];

    protected $data = [
        'code'                      => null,
        'status'                    => null,
        'partlyRedeemable'          => null,
        'articleID'                 => null,
        'articleName'               => null,
        'articleDescription'        => null,
        'articleType'               => null,
        'source'                    => null,
        'initialAmount'             => null,
        'initialAmountValueScaleId' => null,
        'currentAmount'             => null,
        'currency'                  => null,
        'taxRate'                   => null,
        'bonusPoints'               => null,
        'pinRequired'               => null,
        'validUntil'                => null,
        'orderID'                   => null,
        'paymentMethod'             => null,
        'billingDate'               => null,
        'redemptionHistory'         => [],
        'bonusPointChargeHistory'   => [],
        'restriction'               => null,
        'customer'                  => null,
        'redemptionUnits'           => null,
    ];

    public function getCode(): mixed
    {
        return $this->data['code'];
    }

    public function setCode(mixed $code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    public function getStatus(): mixed
    {
        return $this->data['status'];
    }

    public function setStatus(mixed $status)
    {
        $this->data['status'] = $status;
        return $this;
    }

    public function getPartlyRedeemable(): mixed
    {
        return $this->data['partlyRedeemable'];
    }

    public function setPartlyRedeemable(mixed $partlyRedeemable)
    {
        $this->data['partlyRedeemable'] = $partlyRedeemable;
        return $this;
    }

    public function getArticleID(): mixed
    {
        return $this->data['articleID'];
    }

    public function setArticleID(mixed $articleID)
    {
        $this->data['articleID'] = $articleID;
        return $this;
    }

    public function getArticleName(): mixed
    {
        return $this->data['articleName'];
    }

    public function setArticleName(mixed $articleName)
    {
        $this->data['articleName'] = $articleName;
        return $this;
    }

    public function getArticleDescription(): mixed
    {
        return $this->data['articleDescription'];
    }

    public function setArticleDescription(mixed $articleDescription)
    {
        $this->data['articleDescription'] = $articleDescription;
        return $this;
    }

    public function getArticleType(): mixed
    {
        return $this->data['articleType'];
    }

    public function setArticleType(mixed $articleType)
    {
        $this->data['articleType'] = $articleType;
        return $this;
    }

    public function getSource(): mixed
    {
        return $this->data['source'];
    }

    public function setSource(mixed $source)
    {
        $this->data['source'] = $source;
        return $this;
    }

    public function getInitialAmount(): mixed
    {
        return $this->data['initialAmount'];
    }

    public function setInitialAmount(mixed $initialAmount)
    {
        $this->data['initialAmount'] = $initialAmount;
        return $this;
    }

    public function getInitialAmountValueScaleId(): mixed
    {
        return $this->data['initialAmountValueScaleId'];
    }

    public function setInitialAmountValueScaleId(mixed $initialAmountValueScaleId)
    {
        $this->data['initialAmountValueScaleId'] = $initialAmountValueScaleId;
        return $this;
    }

    public function getCurrentAmount(): mixed
    {
        return $this->data['currentAmount'];
    }

    public function setCurrentAmount(mixed $currentAmount)
    {
        $this->data['currentAmount'] = $currentAmount;
        return $this;
    }

    public function getCurrency(): mixed
    {
        return $this->data['currency'];
    }

    public function setCurrency(mixed $currency)
    {
        $this->data['currency'] = $currency;
        return $this;
    }

    public function getTaxRate(): mixed
    {
        return $this->data['taxRate'];
    }

    public function setTaxRate(mixed $taxRate)
    {
        $this->data['taxRate'] = $taxRate;
        return $this;
    }

    public function getBonusPoints(): mixed
    {
        return $this->data['bonusPoints'];
    }

    public function setBonusPoints(mixed $bonusPoints)
    {
        $this->data['bonusPoints'] = $bonusPoints;
        return $this;
    }

    public function getPinRequired(): mixed
    {
        return $this->data['pinRequired'];
    }

    public function setPinRequired(mixed $pinRequired)
    {
        $this->data['pinRequired'] = $pinRequired;
        return $this;
    }

    public function getValidUntil(): mixed
    {
        return $this->data['validUntil'];
    }

    public function setValidUntil(mixed $validUntil)
    {
        $this->data['validUntil'] = $validUntil;
        return $this;
    }

    public function getOrderID(): mixed
    {
        return $this->data['orderID'];
    }

    public function setOrderID(mixed $orderID)
    {
        $this->data['orderID'] = $orderID;
        return $this;
    }

    public function getPaymentMethod(): mixed
    {
        return $this->data['paymentMethod'];
    }

    public function setPaymentMethod(mixed $paymentMethod)
    {
        $this->data['paymentMethod'] = $paymentMethod;
        return $this;
    }

    public function getBillingDate(): mixed
    {
        return $this->data['billingDate'];
    }

    public function setBillingDate(mixed $billingDate)
    {
        $this->data['billingDate'] = $billingDate;
        return $this;
    }

    public function getRedemptionHistory(): mixed
    {
        return $this->data['redemptionHistory'];
    }

    public function setRedemptionHistory(mixed $redemptionHistory)
    {
        $this->data['redemptionHistory'] = $redemptionHistory;
        return $this;
    }

    public function getBonusPointChargeHistory(): mixed
    {
        return $this->data['bonusPointChargeHistory'];
    }

    public function setBonusPointChargeHistory(mixed $bonusPointChargeHistory)
    {
        $this->data['bonusPointChargeHistory'] = $bonusPointChargeHistory;
        return $this;
    }

    public function getRestriction(): mixed
    {
        return $this->data['restriction'];
    }

    public function setRestriction(mixed $restriction)
    {
        $this->data['restriction'] = $restriction;
        return $this;
    }

    public function getCustomer(): mixed
    {
        return $this->data['customer'];
    }

    public function setCustomer(mixed $customer)
    {
        $this->data['customer'] = $customer;
        return $this;
    }

    public function getRedemptionUnits(): mixed
    {
        return $this->data['redemptionUnits'];
    }

    public function setRedemptionUnits(mixed $redemptionUnits)
    {
        $this->data['redemptionUnits'] = $redemptionUnits;
        return $this;
    }

    public function hasError(): bool
    {
        return $this->getStatus() != self::VALID || $this->isSponsoring();
    }

    public function isSponsoring(): bool
    {
        return $this->getStatus() == self::VALID && $this->getCurrentAmount() == 0;
    }

    public function getErrorMessage($lang = 'en'): string
    {
        if ($this->isSponsoring()) {
            return self::STATUS_MAP[$lang][self::SPONSORING] ?? self::SPONSORING;
        }
        $status = $this->getStatus();
        return self::STATUS_MAP[$lang][$status] ?? $status;
    }

    public function getPartlyRedeemableMessage($lang = 'en'): string
    {
        return self::STATUS_MAP[$lang][self::PARTLY_REDEEMABLE] ?? self::PARTLY_REDEEMABLE;
    }

}


