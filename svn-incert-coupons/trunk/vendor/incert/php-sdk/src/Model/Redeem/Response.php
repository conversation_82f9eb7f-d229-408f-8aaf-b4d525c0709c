<?php
/**
 * IncertCommonApiModelRedeemResponse
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class Response extends AbstractModel
{

    protected $data = [
        'redemptionID'   => null,
        'redeemedAmount' => null,
    ];

    public function getRedemptionID(): mixed
    {
        return $this->data['redemptionID'];
    }

    public function setRedemptionID(mixed $redemptionID)
    {
        $this->data['redemptionID'] = $redemptionID;
        return $this;
    }

    public function getRedeemedAmount(): mixed
    {
        return $this->data['redeemedAmount'];
    }

    public function setRedeemedAmount(mixed $redeemedAmount)
    {
        $this->data['redeemedAmount'] = $redeemedAmount;
        return $this;
    }

}


