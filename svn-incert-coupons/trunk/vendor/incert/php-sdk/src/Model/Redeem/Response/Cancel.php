<?php
/**
 * IncertCommonApiModelRedeemCancelResponse
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem\Response;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemCancelResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 * @implements \ArrayAccess<string, mixed>
 */
class Cancel extends AbstractModel
{

    protected $data = [
        'success'       => null,
        'code'          => null,
        'currentAmount' => null,
    ];

    public function getSuccess(): mixed
    {
        return $this->data['success'];
    }

    public function setSuccess(mixed $success)
    {
        $this->data['success'] = $success;
        return $this;
    }

    public function getCode(): mixed
    {
        return $this->data['code'];
    }

    public function setCode(mixed $code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    public function getCurrentAmount(): mixed
    {
        return $this->data['currentAmount'];
    }

    public function setCurrentAmount(mixed $current_amount)
    {
        $this->data['currentAmount'] = $current_amount;
        return $this;
    }

}


