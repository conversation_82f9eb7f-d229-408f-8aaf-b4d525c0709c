<?php
/**
 * IncertCommonApiModelRedeemCustomer
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemCustomer Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class Customer extends AbstractModel
{
    protected $data = [
        'name'          => null,
        'firstName'     => null,
        'lastName'      => null,
        'emailAddress'  => null,
        'streetAddress' => null,
        'city'          => null,
        'postCode'      => null,
        'country'       => null,
        'telephone'     => null,
        'dob'           => null,
        'company'       => null,
    ];

    public function getName(): mixed
    {
        return $this->data['name'];
    }

    public function setName(mixed $name)
    {
        $this->data['name'] = $name;
        return $this;
    }

    public function getFirstName(): mixed
    {
        return $this->data['firstName'];
    }

    public function setFirstName(mixed $firstName)
    {
        $this->data['firstName'] = $firstName;
        return $this;
    }

    public function getLastName(): mixed
    {
        return $this->data['lastName'];
    }

    public function setLastName(mixed $lastName)
    {
        $this->data['lastName'] = $lastName;
        return $this;
    }

    public function getEmailAddress(): mixed
    {
        return $this->data['emailAddress'];
    }

    public function setEmailAddress(mixed $emailAddress)
    {
        $this->data['emailAddress'] = $emailAddress;
        return $this;
    }

    public function getStreetAddress(): mixed
    {
        return $this->data['streetAddress'];
    }

    public function setStreetAddress(mixed $streetAddress)
    {
        $this->data['streetAddress'] = $streetAddress;
        return $this;
    }

    public function getCity(): mixed
    {
        return $this->data['city'];
    }

    public function setCity(mixed $city)
    {
        $this->data['city'] = $city;
        return $this;
    }

    public function getPostCode(): mixed
    {
        return $this->data['postCode'];
    }

    public function setPostCode(mixed $postCode)
    {
        $this->data['postCode'] = $postCode;
        return $this;
    }

    public function getCountry(): mixed
    {
        return $this->data['country'];
    }

    public function setCountry(mixed $country)
    {
        $this->data['country'] = $country;
        return $this;
    }

    public function getTelephone(): mixed
    {
        return $this->data['telephone'];
    }

    public function setTelephone(mixed $telephone)
    {
        $this->data['telephone'] = $telephone;
        return $this;
    }

    public function getDob(): mixed
    {
        return $this->data['dob'];
    }

    public function setDob(mixed $dob)
    {
        $this->data['dob'] = $dob;
        return $this;
    }

    public function getCompany(): mixed
    {
        return $this->data['company'];
    }

    public function setCompany(mixed $company)
    {
        $this->data['company'] = $company;
        return $this;
    }

}


