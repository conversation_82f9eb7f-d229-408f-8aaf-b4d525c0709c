<?php

namespace Incert\Client\Model\Redemption;

use Incert\Client\AbstractModel;

class RechargeVoucher extends AbstractModel
{

    protected $data = [
        "code"             => null,
        "amount"           => null,
        "currency"         => null,
        "bookingID"        => null,
        "comment"          => null,
        "bookingPartnerID" => null,
    ];
    
    /**
     * @return null
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param null $code
     * @return RechargeVoucher
     */
    public function setCode($code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    /**
     * @return null
     */
    public function getAmount()
    {
        return $this->data['amount'];
    }

    /**
     * @param null $amount
     * @return RechargeVoucher
     */
    public function setAmount($amount)
    {
        $this->data['amount'] = $amount;
        return $this;
    }

    /**
     * @return null
     */
    public function getCurrency()
    {
        return $this->data['currency'];
    }

    /**
     * @param null $currency
     * @return RechargeVoucher
     */
    public function setCurrency($currency)
    {
        $this->data['currency'] = $currency;
        return $this;
    }

    /**
     * @return null
     */
    public function getBookingID()
    {
        return $this->data['bookingID'];
    }

    /**
     * @param null $bookingID
     * @return RechargeVoucher
     */
    public function setBookingID($bookingID)
    {
        $this->data['bookingID'] = $bookingID;
        return $this;
    }

    /**
     * @return null
     */
    public function getComment()
    {
        return $this->data['comment'];
    }

    /**
     * @param null $comment
     * @return RechargeVoucher
     */
    public function setComment($comment)
    {
        $this->data['comment'] = $comment;
        return $this;
    }

    /**
     * @return null
     */
    public function getBookingPartnerID()
    {
        return $this->data['bookingPartnerID'];
    }

    /**
     * @param null $bookingPartnerID
     * @return RechargeVoucher
     */
    public function setBookingPartnerID($bookingPartnerID)
    {
        $this->data['bookingPartnerID'] = $bookingPartnerID;
        return $this;
    }
    
}