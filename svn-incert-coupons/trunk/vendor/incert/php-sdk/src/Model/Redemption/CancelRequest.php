<?php
/**
 * IncertCommonApiModelCancelRedemptionRequest
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redemption;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelCancelRedemptionRequest Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class CancelRequest extends AbstractModel
{
    protected $data = [
        'redemptionID' => null,
    ];

    public function getRedemptionID(): mixed
    {
        return $this->data['redemptionID'];
    }

    public function setRedemptionID(mixed $redemptionID)
    {
        $this->data['redemptionID'] = $redemptionID;
        return $this;
    }

}


