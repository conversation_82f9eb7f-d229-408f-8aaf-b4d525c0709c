<?php
/**
 * IncertCommonApiModulesOAuthModelClientCredentialsGrantAccessTokenResponse
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Incert\Client
 */


namespace Incert\Client\OAuth20;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModulesOAuthModelClientCredentialsGrantAccessTokenResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class GrantAccessTokenResponse extends AbstractModel
{

   protected $data = [
       'token_type' => 'Bearer',
       'expires_in' => 3600,
       'access_token' => null
   ];

    public function getTokenType(): mixed
    {
        return $this->data['token_type'];
    }

    public function setTokenType(mixed $tokenType)
    {
        $this->data['token_type'] = $tokenType;
        return $this;
    }

    public function getExpiresIn(): mixed
    {
        return $this->data['expires_in'];
    }

    public function setExpiresIn(mixed $expiresIn)
    {
        $this->data['expires_in'] = $expiresIn;
        return $this;
    }

    public function getAccessToken(): mixed
    {
        return $this->data['access_token'];
    }

    public function setAccessToken(mixed $accessToken)
    {
        $this->data['access_token'] = $accessToken;
        return $this;
    }


}


