<?php
/**
 * IncertCommonApiModulesOAuthModelClientCredentialsGrantAccessTokenRequest
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\OAuth20;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModulesOAuthModelClientCredentialsGrantAccessTokenRequest Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class GrantAccessTokenRequest extends AbstractModel
{

    protected $data = [
        'grant_type'    => 'client_credentials',
        'scope'        => 'gms',
        'client_id'     => null,
        'client_secret' => null,
    ];

    public function getGrantType(): mixed
    {
        return $this->data['grant_type'];
    }

    public function setGrantType(mixed $grandType)
    {
        $this->data['grant_type'] = $grandType;
        return $this;
    }

    public function getScope(): mixed
    {
        return $this->data['scope'];
    }

    public function setScope(mixed $scope)
    {
        $this->data['scope'] = $scope;
        return $this;
    }

    public function getClientId(): mixed
    {
        return $this->data['client_id'];
    }

    public function setClientId(mixed $clientId)
    {
        $this->data['client_id'] = $clientId;
        return $this;
    }

    public function getClientSecret(): mixed
    {
        return $this->data['client_secret'];
    }

    public function setClientSecret(mixed $clientSecret)
    {
        $this->data['client_secret'] = $clientSecret;
        return $this;
    }

}


