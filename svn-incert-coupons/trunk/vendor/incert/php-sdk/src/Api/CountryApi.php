<?php
/**
 * CountryApi
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Api;

use Incert\Client\Api\Exception\ApiException;

/**
 * CountryApi Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class CountryApi extends BaseApi
{


    /**
     * @param        $countryRequest
     * @return mixed
     * @throws ApiException
     */
    public function shopV2CountryExistsPost(
        $countryRequest = null
    ) {
        [$response] = $this->shopV2CountryExistsPostWithHttpInfo($countryRequest);
        return $response;
    }

    /**
     * @param        $countryRequest
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function shopV2CountryExistsPostWithHttpInfo(
        $countryRequest = null
    ) {
        $request = $this->getRequest('/v2/country/exists', [
            'request_method' => 'POST',
            'form_params' => $countryRequest
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Country\Response::class);
    }

    /**
     * @return mixed
     * @throws ApiException
     */
    public function shopV2CountryActiveGet() {
        [$response] = $this->shopV2CountryActiveGetWithHttpInfo();
        return $response;
    }

    /**
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2CountryActiveGetWithHttpInfo() {

        $request = $this->getRequest('/v2/country/active');
        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Country\Response\Collection::class);
    }
}
