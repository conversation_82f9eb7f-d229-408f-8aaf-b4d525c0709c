=== incert Voucher Management System ===
Contributors: incertat
Donate link: https://www.incert.at
Tags: woocommerce, vouchers, coupons, api, incert, pdf, pkpass, fulfillment
Requires at least: 6.4
Tested up to: 6.7
Requires PHP: 7.4
Stable tag: 1.0.3
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Professional voucher sales & redemption for WooCommerce – powered by INCERT.

Professioneller Gutscheinverkauf und -einlösung in WooCommerce – powered by INCERT.

== Description ==

=== English ===

The official plugin to connect WooCommerce with the **INCERT Voucher Management System**.

Are you currently selling gift vouchers in your WooCommerce shop? WooCommerce’s native features are limited – vouchers are treated like simple products with no real personalization, automation, fulfillment, or proper accounting integrations.

This plugin brings professional voucher handling to your shop:
Connect to the INCERT system via API and enable personalized, digital and physical vouchers with real-time generation, delivery and redemption.

**Key features:**
* Personalized digital vouchers (PDF, QR Code, PKPass / Apple Wallet)
* Physical gift cards & lettershop fulfillment
* API-based voucher creation and tracking
* Customer personalization (design, image upload, text)
* Voucher delivery via email or print
* WooCommerce cart redemption
* INCERT admin dashboard with reports & exports
* Optional sales landing pages via INCERT

=== Deutsch ===

Professioneller GUTSCHEIN-Verkauf & GUTSCHEIN-Handling mit dem INCERT- WooCommerce-Plugin

Verkaufen Sie bereits Gutscheine in Ihrem WooCommerce-Shop? Standardmäßig ist WooCommerce dafür kaum geeignet: Es fehlen professionelle Funktionen zur Personalisierung, automatischen Erstellung, Verwaltung oder steuerlich relevanten Abbildung.

Dieses Plugin verbindet Ihren Shop mit dem INCERT-System über eine leistungsstarke API.

**Funktionen:**
* Verkauf von digitalen Wertgutscheinen als PDF, QR-Code oder PKPass (Wallet)
* Personalisierbare Gutscheine mit Bild, Text & Widmung
* Automatisierte Fulfillment-Prozesse (E-Mail oder Post)
* Printgutscheine mit weltweitem Versand über Lettershop
* Gutscheineinlösung im WooCommerce-Warenkorb
* Professionelles Admin-Panel mit Reporting & BUHA-Auswertung
* Unterstützung für eigene Gutschein-Landingpages via INCERT

== Installation ==

=== English ===

1. Upload the plugin to the `/wp-content/plugins/` directory.
2. Activate the plugin through the "Plugins" menu in WordPress.
3. Go to the plugin settings and enter your INCERT API credentials.
4. Link your WooCommerce products with INCERT voucher templates.

=== Deutsch ===

1. Laden Sie das Plugin in das Verzeichnis `/wp-content/plugins/` hoch.
2. Aktivieren Sie das Plugin über das "Plugins"-Menü in WordPress.
3. Gehen Sie zu den Plugin-Einstellungen und geben Sie Ihre INCERT-API-Zugangsdaten ein.
4. Verknüpfen Sie Ihre WooCommerce-Produkte mit INCERT-Gutscheinvorlagen.

== Frequently Asked Questions ==

= Do I need an INCERT account? =
Yes. A valid INCERT account and API access are required. Contact [https://www.incert.at](https://www.incert.at) to get started.

= Can I sell physical gift cards? =
Yes. Through lettershop integration, INCERT can produce and ship physical vouchers worldwide.

= Does it support Apple Wallet? =
Yes. Digital vouchers can be delivered as PKPass files for Apple Wallet.

= What if I don’t have API credentials yet? =
Reach out to INCERT for onboarding and credential provisioning.

=== Deutsch ===

= Brauche ich ein INCERT-Konto? =
Ja, zur Nutzung des Plugins wird ein INCERT-Account mit API-Zugang benötigt. Infos unter [https://www.incert.at](https://www.incert.at).

= Kann ich auch physische Gutscheine verkaufen? =
Ja. INCERT bietet eine Lettershop-Anbindung für Printgutscheine inkl. weltweitem Versand.

= Wird Apple Wallet unterstützt? =
Ja. Digitale Gutscheine können im PKPass-Format für Wallet erstellt werden.

= Was tun, wenn ich noch keine API-Zugangsdaten habe? =
Wenden Sie sich direkt an INCERT für Zugangsdaten und Integration.

== Screenshots ==

1. Plugin settings with API integration (Einstellungen mit API-Verbindung)
2. Voucher product with personalization fields (Gutscheinprodukt mit Personalisierung)
3. PDF voucher example with QR code (PDF-Gutschein mit QR-Code)
4. Admin dashboard with reporting and BUHA (Admin-Panel mit Auswertungen)
5. Format selection in WooCommerce product (Formatauswahl im Produkt)
6. Voucher redemption in WooCommerce cart (Einlösung im Warenkorb)

== Changelog ==

=== English ===

= 1.0.3 =
* Improved API integration
* Enhanced WooCommerce compatibility
* Bug fixes and performance improvements

= 1.0.2 =
* Fix API

= 1.0.0 =
* Initial release

=== Deutsch ===

= 1.0.3 =
* Verbesserte API-Integration
* Erweiterte WooCommerce-Kompatibilität
* Fehlerbehebungen und Leistungsverbesserungen

= 1.0.2 =
* API-Fehler behoben

= 1.0.0 =
* Erstveröffentlichung

== Upgrade Notice ==

=== English ===

= 1.0.3 =
This update improves API integration, enhances WooCommerce compatibility, and includes important bug fixes and performance improvements.

= 1.0.2 =
Fixed minor API issues and improved WooCommerce compatibility.

=== Deutsch ===

= 1.0.3 =
Dieses Update verbessert die API-Integration, erweitert die WooCommerce-Kompatibilität und enthält wichtige Fehlerbehebungen und Leistungsverbesserungen.

= 1.0.2 =
Kleinere API-Probleme behoben und WooCommerce-Kompatibilität verbessert.

== Support ==

Not an INCERT customer yet or missing API credentials?
→ Visit [https://www.incert.at](https://www.incert.at) or get in touch via our contact form.

Noch kein INCERT-Kunde oder benötigen Sie API-Zugang?
→ Besuchen Sie [https://www.incert.at](https://www.incert.at) oder kontaktieren Sie uns direkt.
