<?php
/**
 * OAuth20ClientCredentialsApi
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Api;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Incert\Client\Api\Exception\ApiException;
use Incert\Client\Configuration;
use Incert\Client\HeaderSelector;

/**
 * OAuth20ClientCredentialsApi Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class OAuth20ClientCredentialsApi
{
    /**
     * @var ClientInterface
     */
    protected $client;

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var HeaderSelector
     */
    protected $headerSelector;

    /** @var string[] $contentTypes * */
    public const contentTypes = [
        'oauthAccessTokenPost' => [
            'application/json',
        ],
    ];

    /**
     * @param ClientInterface $client
     * @param Configuration   $config
     * @param HeaderSelector  $selector
     */
    public function __construct(
        Configuration $config = null,
        ClientInterface $client = null,
        HeaderSelector $selector = null
    ) {
        $this->config = $config ?: new Configuration();
        $this->client = $client ?: new Client();
        $this->headerSelector = $selector ?: new HeaderSelector();
    }

    /**
     * @return Configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * @param        $grantAccessTokenRequest
     * @param string $contentType
     * @return mixed|\Incert\Client\OAuth20\GrantAccessTokenResponse
     * @throws ApiException
     */
    public function oauthAccessTokenPost(
        $grantAccessTokenRequest = null,
        string $contentType = self::contentTypes['oauthAccessTokenPost'][0]
    ) {
        [$response] = $this->oauthAccessTokenPostWithHttpInfo($grantAccessTokenRequest, $contentType);
        return $response;
    }

    /**
     * @param        $grantAccessTokenRequest
     * @param string $contentType
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function oauthAccessTokenPostWithHttpInfo(
        $grantAccessTokenRequest = null,
        string $contentType = self::contentTypes['oauthAccessTokenPost'][0]
    ) {
        $request = $this->oauthAccessTokenPostRequest($grantAccessTokenRequest, $contentType);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    (int)$e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? (string)$e->getResponse()->getBody() : null
                );
            } catch (ConnectException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    (int)$e->getCode(),
                    null,
                    null
                );
            }

            $statusCode = $response->getStatusCode();

            switch ($statusCode) {
                case 200:
                    $content = (string)$response->getBody();
                    return [
                        new \Incert\Client\OAuth20\GrantAccessTokenResponse(json_decode($content, true)),
                        $response->getStatusCode(),
                        $response->getHeaders(),
                    ];
                case 400:
                    $content = (string)$response->getBody();
                    return [
                        new \Incert\Client\Model\BadRequestResponse(json_decode($content, true)),
                        $response->getStatusCode(),
                        $response->getHeaders(),
                    ];
                case 401:
                    $content = (string)$response->getBody();
                    return [
                        new \Incert\Client\OAuth20\UnauthorizedResponse(json_decode($content, true)),
                        $response->getStatusCode(),
                        $response->getHeaders(),
                    ];
            }

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        (string)$request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    (string)$response->getBody()
                );
            }

            $content = (string)$response->getBody();
            try {
                $content = json_decode($content, false, 512, JSON_THROW_ON_ERROR);
            } catch (\JsonException $exception) {
                throw new ApiException(
                    sprintf(
                        'Error JSON decoding server response (%s)',
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $content
                );
            }

            return [
                $content,
                $response->getStatusCode(),
                $response->getHeaders(),
            ];
        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $e->setResponseObject([
                        $e->getResponseBody(),
                        'Incert\Client\OAuth20\GrantAccessTokenResponse',
                        $e->getResponseHeaders()
                    ]);
                    break;
                case 400:
                    $e->setResponseObject([
                        $e->getResponseBody(),
                        '\Incert\Client\Model\BadRequestResponse',
                        $e->getResponseHeaders()
                    ]);
                    break;
                case 401:
                    $e->setResponseObject([
                        $e->getResponseBody(),
                        '\Incert\Client\OAuth20\UnauthorizedResponse',
                        $e->getResponseHeaders()
                    ]);
                    break;
            }
            throw $e;
        }
    }

    /**
     * @param  $grantAccessTokenRequest
     * @param string $contentType
     * @return Request
     */
    public function oauthAccessTokenPostRequest(
        $grantAccessTokenRequest = null,
        string $contentType = self::contentTypes['oauthAccessTokenPost'][0]
    ) {
        $resourcePath = '/oauth/accessToken';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        $headers = $this->headerSelector->selectHeaders(
            ['application/json',],
            $contentType,
            $multipart
        );

        // for model (json/xml)
        if (isset($grantAccessTokenRequest)) {
            if (stripos($headers['Content-Type'], 'application/json') !== false) {
                # if Content-Type contains "application/json", json_encode the body
                $httpBody = \GuzzleHttp\Utils::jsonEncode($grantAccessTokenRequest->getData());
            } else {
                $httpBody = $grantAccessTokenRequest;
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $formParamValueItems = is_array($formParamValue) ? $formParamValue : [$formParamValue];
                    foreach ($formParamValueItems as $formParamValueItem) {
                        $multipartContents[] = [
                            'name' => $formParamName,
                            'contents' => $formParamValueItem,
                        ];
                    }
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);
            } elseif (stripos($headers['Content-Type'], 'application/json') !== false) {
                # if Content-Type contains "application/json", json_encode the form parameters
                $httpBody = \GuzzleHttp\Utils::jsonEncode($formParams);
            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\Query::build($formParams, \PHP_QUERY_RFC3986);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('X-Api-Key');
        if ($apiKey !== null) {
            $headers['X-Api-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $operationHost = $this->config->getHost();
        $query = \GuzzleHttp\Psr7\Query::build($queryParams, \PHP_QUERY_RFC3986);
        return new Request(
            'POST',
            $operationHost . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Create http client option
     * @return array of http client options
     * @throws \RuntimeException on file opening failure
     */
    protected function createHttpClientOption()
    {
        $options = [];
        if ($this->config->getDebug()) {
            $options[RequestOptions::DEBUG] = fopen($this->config->getDebugFile(), 'a');
            if (!$options[RequestOptions::DEBUG]) {
                throw new \RuntimeException('Failed to open the debug file: ' . $this->config->getDebugFile());
            }
        }

        return $options;
    }
}
