<?php
/**
 * ShopApi
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Api;

use Incert\Client\Api\Exception\ApiException;
use Incert\Client\Model\Shop\Request\Order;
use Incert\Client\Model\Shop\Request\Product\Cancel as ProductCancel;
use Incert\Client\Model\Shop\Request\Voucher\Cancel as VoucherCancel;

/**
 * RedemptionApi Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class ShopApi extends BaseApi
{

    /**
     * @return mixed
     * @throws ApiException
     */
    public function shopV2CoreProductGet(
        $countryCode,
        $currency,
        $lang = null
    ) {
        [$response] = $this->shopV2CoreProductGetWithHttpInfo($countryCode, $currency, $lang);
        return $response;
    }

    /**
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2CoreProductGetWithHttpInfo(
        $countryCode,
        $currency,
        $lang = null
    ) {

        // verify the required parameter 'countryCode' is set
        if (!$countryCode) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $countryCode when calling shopV2CoreProductGet'
            );
        }

        // verify the required parameter 'currency' is set
        if (!$currency) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $currency when calling shopV2CoreProductGet'
            );
        }

        $request = $this->getRequest('/shop/v2/core/product', [
            'query_params' => array_filter(['countryCode' => $countryCode, 'currency' => $currency, 'lang' => $lang ]),
        ]);


        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Product\Collection::class);
    }


    /**
     * @return mixed
     * @throws ApiException
     */
    public function shopV2VoucherDownloadGet(
        $orderId,
        $voucherCode,
        $lang = null
    ) {
        [$response] = $this->shopV2VoucherDownloadGetWithHttpInfo($orderId, $voucherCode, $lang);
        return $response;
    }

    /**
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2VoucherDownloadGetWithHttpInfo( $orderId, $voucherCode, $lang = null) {

        // verify the required parameter 'orderId' is set
        if (!$orderId) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $orderId when calling shopV2VoucherDownloadGet'
            );
        }

        // verify the required parameter 'voucherCode' is set
        if (!$voucherCode) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $voucherCode when calling shopV2VoucherDownloadGet'
            );
        }

        $request = $this->getRequest('/shop/v2/voucher/download/{orderId}/{voucherCode}', [
            'path_params' => ['orderId' => $orderId, 'voucherCode' => $voucherCode],
            'query_params' => array_filter(['lang' => $lang ]),
        ]);


        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Pdf::class, false);
    }

    /**
     * @return mixed
     * @throws ApiException
     */
    public function shopV2ModulesCoreGet(
        $lang = null
    ) {
        [$response] = $this->shopV2ModulesCoreGetWithHttpInfo($lang);
        return $response;
    }

    /**
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2ModulesCoreGetWithHttpInfo($lang = null) {


        $request = $this->getRequest('/shop/v2/modules/core', [
            'query_params' => array_filter(['lang' => $lang ]),
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Core::class);
    }




    /**
     * @param $orderRequest
     * @return mixed
     * @throws ApiException
     */
    public function shopV2OrderPost(
        $countryCode,
        $currency,
        $orderRequest = null
    ) {
        [$response] = $this->shopV2OrderPostWithHttpInfo($countryCode, $currency, $orderRequest);
        return $response;
    }

    /**
     * @param        $orderRequest
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function shopV2OrderPostWithHttpInfo(
        $countryCode,
        $currency,
        $orderRequest = null
    ) {

        // verify the required parameter 'countryCode' is set
        if (!$countryCode) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $countryCode when calling shopV2OrderPost'
            );
        }

        // verify the required parameter 'currency' is set
        if (!$currency) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $currency when calling shopV2OrderPost'
            );
        }

        // verify the required parameter 'orderRequest' is set
        if (! $orderRequest instanceof Order) {
            throw new \InvalidArgumentException(
                'Invalid parameter $orderRequest when calling shopV2OrderPost'
            );
        }

        $request = $this->getRequest('/shop/v2/order', [
            'request_method' => 'POST',
            'query_params' => ['countryCode' => $countryCode, 'currency' => $currency],
            'form_params' => $orderRequest,
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Order::class);
    }


    /**
     * @param $orderRequest
     * @return mixed
     * @throws ApiException
     */
    public function shopV2OrderCalculatePost(
        $countryCode,
        $currency,
        $orderRequest = null
    ) {
        [$response] = $this->shopV2OrderCalculatePostWithHttpInfo($countryCode, $currency, $orderRequest);
        return $response;
    }

    /**
     * @param $countryCode
     * @param $currency
     * @param $orderRequest
     * @return array
     * @throws ApiException
     */
    private function shopV2OrderCalculatePostWithHttpInfo(
        $countryCode,
        $currency,
        $orderRequest = null
    ) {

        // verify the required parameter 'countryCode' is set
        if (!$countryCode) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $countryCode when calling shopV2OrderPost'
            );
        }

        // verify the required parameter 'currency' is set
        if (!$currency) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $currency when calling shopV2OrderPost'
            );
        }

        // verify the required parameter 'orderRequest' is set
        if (! $orderRequest instanceof Order) {
            throw new \InvalidArgumentException(
                'Invalid parameter $orderRequest when calling shopV2OrderPost'
            );
        }

        $request = $this->getRequest('/shop/v2/order/calculate', [
            'request_method' => 'POST',
            'query_params' => ['countryCode' => $countryCode, 'currency' => $currency],
            'form_params' => $orderRequest,
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Order\Validate::class);
    }

    /**
     * @return mixed
     * @throws ApiException
     */
    public function shopV2OrderCanCancelGet(
        $orderId,
        $itemId = null
    ) {
        [$response] = $this->shopV2OrderCanCancelGetWithHttpInfo($orderId, $itemId);
        return $response;
    }

    /**
     * @param $orderId
     * @param $itemId
     * @return array
     * @throws ApiException
     */
    public function shopV2OrderCanCancelGetWithHttpInfo($orderId, $itemId = null)
    {
        $request = $this->getRequest('/shop/v2/order/canCancel', [
            'query_params' => array_filter(['orderId' => $orderId, 'itemId' => $itemId ]),
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Order\CanCancel::class);
    }

    /**
     * @param $orderId
     * @param $comment
     * @return mixed
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2OrderCancelPost(
        $orderId,
        $comment = null,
    ) {
        [$response] = $this->shopV2OrderCancelPostWithHttpInfo($orderId, $comment);
        return $response;
    }

    /**
     * @param $orderId
     * @param $comment
     * @return array
     * @throws ApiException
     */
    private function shopV2OrderCancelPostWithHttpInfo(
        $orderId,
        $comment = null
    ) {

        if (!$orderId) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $orderId when calling shopV2OrderCancelPost'
            );
        }

        $request = $this->getRequest('/shop/v2/order/cancel/{orderId}', [
            'request_method' => 'POST',
            'path_params' => ['orderId' => $orderId],
            'query_params' => array_filter(['comment' => $comment]),
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Order\Cancel::class);
    }


    /**
     * @param $orderId
     * @param $request
     * @return mixed
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2ProductCancelPost(
        $orderId,
        $request
    ) {
        [$response] = $this->shopV2ProductCancelPostWithHttpInfo($orderId, $request);
        return $response;
    }

    /**
     * @param $orderId
     * @param $cancelRequest
     * @return array
     * @throws ApiException
     */
    private function shopV2ProductCancelPostWithHttpInfo(
        $orderId,
        $cancelRequest
    ) {

        if (!$orderId) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $orderId when calling shopV2ProductCancelPost'
            );
        }

        if (!$cancelRequest instanceof ProductCancel) {
            throw new \InvalidArgumentException(
                'Invalid parameter $request when calling shopV2ProductCancelPost'
            );
        }

        $request = $this->getRequest('/shop/v2/product/cancel/{orderId}', [
            'request_method' => 'POST',
            'path_params' => ['orderId' => $orderId],
            'form_params' => $cancelRequest,
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Product\Cancel::class);
    }

    /**
     * @param $orderId
     * @param $request
     * @return mixed
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2VoucherCancelPost(
        $orderId,
        $request,
    ) {
        [$response] = $this->shopV2VoucherCancelPostWithHttpInfo($orderId, $request);
        return $response;
    }

    /**
     * @param $orderId
     * @param $cancelRequest
     * @return array
     * @throws ApiException
     */
    private function shopV2VoucherCancelPostWithHttpInfo(
        $orderId,
        $cancelRequest
    ) {

        if (!$orderId) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $orderId when calling shopV2VoucherCancelPost'
            );
        }

        if (!$cancelRequest instanceof VoucherCancel) {
            throw new \InvalidArgumentException(
                'Invalid parameter $request when calling shopV2VoucherCancelPost'
            );
        }

        $request = $this->getRequest('/shop/v2/voucher/cancel/{orderId}', [
            'request_method' => 'POST',
            'path_params' => ['orderId' => $orderId],
            'form_params' => $cancelRequest,
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Shop\Response\Voucher\Cancel::class);
    }

}
