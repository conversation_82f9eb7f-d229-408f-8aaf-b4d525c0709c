<?php
/**
 * RedemptionApi
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Api;

use Incert\Client\Api\Exception\ApiException;

/**
 * RedemptionApi Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class RedemptionApi extends BaseApi
{

    /**
     * @param        $cancelRedemptionRequest
     * @return mixed
     * @throws ApiException
     */
    public function shopV2RedeemCancelPost(
        $cancelRedemptionRequest = null
    ) {
        [$response] = $this->shopV2RedeemCancelPostWithHttpInfo($cancelRedemptionRequest);
        return $response;
    }

    /**
     * @param        $cancelRedemptionRequest
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function shopV2RedeemCancelPostWithHttpInfo(
        $cancelRedemptionRequest = null
    ) {
        $request = $this->getRequest('/shop/v2/redeem/cancel', [
            'request_method' => 'POST',
            'form_params' => $cancelRedemptionRequest
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Redeem\Response\Cancel::class);
    }

    /**
     * @param        $rechargeVoucherRequest
     * @return mixed
     * @throws ApiException
     */
    public function shopV2RedeemRechargeVoucherPost(
        $rechargeVoucherRequest = null
    ) {
        [$response] = $this->shopV2RedeemmRechargeVoucherPostWithHttpInfo($rechargeVoucherRequest);
        return $response;
    }

    /**
     * @param  $rechargeVoucherRequest
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function shopV2RedeemmRechargeVoucherPostWithHttpInfo(
        $rechargeVoucherRequest = null
    ) {
        $request = $this->getRequest('/shop/v2/redeem/rechargeVoucher', [
            'request_method' => 'POST',
            'form_params' => $rechargeVoucherRequest
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Redeem\Response\RechargeVoucher::class);
    }

    /**
     * Operation shopV2RedeemPost
     * Redeems a voucher.
     */
    public function shopV2RedeemPost(
        $redeemRequest = null,
    ) {
        [$response] = $this->shopV2RedeemPostWithHttpInfo($redeemRequest);
        return $response;
    }

    /**
     * @param        $redeemRequest
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2RedeemPostWithHttpInfo(
        $redeemRequest = null
    ) {

        $request = $this->getRequest('/shop/v2/redeem', [
            'request_method' => 'POST',
            'form_params' => $redeemRequest
        ]);

        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Redeem\Response::class);
    }

    /**
     * @param        $code
     * @param        $bookingPartnerId
     * @return mixed
     * @throws ApiException
     */
    public function shopV2RedeemStatusCodeGet(
        $code,
        $bookingPartnerId = null
    ) {
        [$response] = $this->shopV2RedeemStatusCodeGetWithHttpInfo($code, $bookingPartnerId);
        return $response;
    }

    /**
     * @param        $code
     * @param        $bookingPartnerId
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2RedeemStatusCodeGetWithHttpInfo(
        $code,
        $bookingPartnerId = null
    ) {

        // verify the required parameter 'code' is set
        if ($code === null || (is_array($code) && count($code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $code when calling shopV2RedeemStatusCodeGet'
            );
        }

        $request = $this->getRequest('/shop/v2/redeem/status/{code}', [
            'path_params' => ['code' => $code],
            'query_params' => array_filter(['bookingPartnerId' => $bookingPartnerId])
        ]);


        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Redeem\Response\Status::class);
    }

    /**
     * @param        $code
     * @param        $bookingPartnerId
     * @param        $persons
     * @param        $nights
     * @param        $value
     * @param        $rateCode
     * @return \Incert\Client\Model\Redeem\Response\StatusDetail
     * @throws ApiException
     */
    public function shopV2RedeemStatusDetailCodeGet(
        $code,
        $bookingPartnerId = null,
        $persons = null,
        $nights = null,
        $value = null,
        $rateCode = null
    ) {
        [$response] = $this->shopV2RedeemStatusDetailCodeGetWithHttpInfo($code, $bookingPartnerId, $persons, $nights,
            $value, $rateCode);
        return $response;
    }

    /**
     * @param        $code
     * @param        $bookingPartnerId
     * @param        $persons
     * @param        $nights
     * @param        $value
     * @param        $rateCode
     * @return array
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function shopV2RedeemStatusDetailCodeGetWithHttpInfo(
        $code,
        $bookingPartnerId = null,
        $persons = null,
        $nights = null,
        $value = null,
        $rateCode = null
    ) {
        // verify the required parameter 'code' is set
        if ($code === null || (is_array($code) && count($code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $code when calling shopV2RedeemStatusDetailCodeGet'
            );
        }

        $request = $this->getRequest('/shop/v2/redeem/status/detail/{code}', [
            'path_params' => ['code' => $code],
            'query_params' => array_filter([
                'bookingPartnerId' => $bookingPartnerId,
                'persons'          => $persons,
                'nights'           => $nights,
                'value'            => $value,
                'rateCode'         => $rateCode,
            ])
        ]);


        return $this->sendRequestAndGetResult($request, \Incert\Client\Model\Redeem\Response\StatusDetail::class);

    }
}
