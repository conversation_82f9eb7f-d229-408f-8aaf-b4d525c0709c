<?php

namespace Incert\Client\Api;

use Guz<PERSON><PERSON>ttp\Client;
use Guzzle<PERSON>ttp\ClientInterface;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Incert\Client\Api\Exception\ApiException;
use Incert\Client\Configuration;
use Incert\Client\HeaderSelector;

abstract class BaseApi
{

    /**
     * @var ClientInterface
     */
    protected $client;

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var HeaderSelector
     */
    protected $headerSelector;


    /**
     * @param ClientInterface $client
     * @param Configuration   $config
     * @param HeaderSelector  $selector
     */
    public function __construct(
        Configuration $config = null,
        ClientInterface $client = null,
        HeaderSelector $selector = null
    ) {
        $this->config = $config ?: new Configuration();
        $this->client = $client ?: new Client();
        $this->headerSelector = $selector ?: new HeaderSelector();
    }

    /**
     * @return Configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Create http client option
     * @return array of http client options
     * @throws \RuntimeException on file opening failure
     */
    protected function createHttpClientOption()
    {
        $options = [];
        if ($this->config->getDebug()) {
            $options[RequestOptions::DEBUG] = fopen($this->config->getDebugFile(), 'a');
            if (!$options[RequestOptions::DEBUG]) {
                throw new \RuntimeException('Failed to open the debug file: ' . $this->config->getDebugFile());
            }
        }

        return $options;
    }

    /**
     * @param \Psr\Http\Message\ResponseInterface $response
     * @param Request                             $request
     * @return mixed
     * @throws ApiException
     */
    public function getContent(\Psr\Http\Message\ResponseInterface $response, Request $request, $asJson = true): mixed
    {
        $content = (string)$response->getBody();
        try {
            if ($asJson) {
                $content = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            }
        } catch (\JsonException $exception) {
            throw new ApiException(
                sprintf(
                    'Error JSON decoding server response (%s)',
                    $request->getUri()
                ),
                $response->getStatusCode(),
                $response->getHeaders(),
                $content
            );
        }
        return $content;
    }

    /**
     * @param $code
     * @param $resourcePath
     * @param $options array containing multipart, content_type, request_method, form_params, header_params, query_params, path_params
     * @return Request
     */
    protected function getRequest(
        $resourcePath,
        $options = []
    ) {

        $httpBody = '';
        $multipart = $options['mulitpart'] ?? false;

        // content type
        $contentType = $options['content_type'] ?? 'application/json';

        // request method
        $requestMethod = $options['request_method'] ?? 'GET';

        // form params
        $formParams = $options['form_params'] ?? [];

        // header params
        $headerParams = $options['header_params'] ?? [];

        // query params
        $queryParams = array_filter($options['query_params'] ?? []);

        // path params
        foreach ($options['path_params'] ?? [] as $key => $param) {
            $resourcePath = str_replace(
                '{' . $key . '}',
                rawurlencode((string)$param),
                $resourcePath
            );
        }

        $headers = $this->headerSelector->selectHeaders(
            ['application/json'],
            $contentType,
            $multipart
        );

        // for model (json/xml)
        if ($multipart && is_countable($formParams) && count($formParams) > 0) {
            $multipartContents = [];
            foreach ($formParams as $formParamName => $formParamValue) {
                $formParamValueItems = is_array($formParamValue) ? $formParamValue : [$formParamValue];
                foreach ($formParamValueItems as $formParamValueItem) {
                    $multipartContents[] = [
                        'name'     => $formParamName,
                        'contents' => $formParamValueItem,
                    ];
                }
            }
            // for HTTP post (form)
            $httpBody = new MultipartStream($multipartContents);
        } elseif (stripos($headers['Content-Type'], 'application/json') !== false) {
            # if Content-Type contains "application/json", json_encode the form parameters
            $httpBody = \GuzzleHttp\Utils::jsonEncode($formParams);
        } else {
            // for HTTP post (form)
            $httpBody = \GuzzleHttp\Psr7\Query::build($formParams, \PHP_QUERY_RFC3986);
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('X-Api-Key');
        if ($apiKey !== null) {
            $headers['X-Api-Key'] = $apiKey;
        }
        // this endpoint requires Bearer authentication (access token)
        if (!empty($this->config->getAccessToken())) {
            $headers['Authorization'] = 'Bearer ' . $this->config->getAccessToken();
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $operationHost = $this->config->getHost();
        $query = \GuzzleHttp\Psr7\Query::build($queryParams, \PHP_QUERY_RFC3986);
        return new Request(
            $requestMethod,
            $operationHost . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * @param Request $request
     * @param array   $options
     * @return \Psr\Http\Message\ResponseInterface
     * @throws ApiException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendRequest(Request $request, array $options): \Psr\Http\Message\ResponseInterface
    {
        try {
            $response = $this->client->send($request, $options);
        } catch (RequestException $e) {
            throw new ApiException(
                "[{$e->getCode()}] {$e->getMessage()}",
                (int)$e->getCode(),
                $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                $e->getResponse() ? (string)$e->getResponse()->getBody() : null
            );
        } catch (ConnectException $e) {
            throw new ApiException(
                "[{$e->getCode()}] {$e->getMessage()}",
                (int)$e->getCode(),
                null,
                null
            );
        }
        return $response;
    }

    /**
     * @param \Psr\Http\Message\ResponseInterface $response
     * @param Request                             $request
     * @return array
     * @throws ApiException
     */
    public function sendRequestAndGetResult(Request $request, $successClassName, $resultAsJson = true): array
    {
        $options = $this->createHttpClientOption();
        try {
            $response = $this->sendRequest($request, $options);
            $content = $this->getContent($response, $request, $resultAsJson);

            return [
                $content ? new $successClassName($content) : $content,
                $response->getStatusCode(),
                $response->getHeaders(),
            ];
        }catch (ApiException $e){
            $statusCode = $e->getCode();
            $params = ['code' => $e->getCode(), 'message' => $e->getMessage(), 'error' => $e];
            if($e->getResponseBody()){
                $jsonBody = json_decode($e->getResponseBody(),true);
                if ($jsonBody) {
                    $params = $jsonBody;
                }
            }
            switch ($statusCode) {
                case 401:
                    return [
                        new \Incert\Client\Model\Response\UnauthorizedResponse($params),
                        $statusCode,
                        $e->getResponseHeaders(),
                    ];
                case 400:
                default:
                    return [
                        new \Incert\Client\Model\Response\BadRequestResponse($params),
                        $statusCode,
                        $e->getResponseHeaders(),
                    ];
            }

        }
    }
}