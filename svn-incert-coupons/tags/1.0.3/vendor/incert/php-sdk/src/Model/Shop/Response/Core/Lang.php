<?php
/**
 * Language
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Core
 */

namespace Incert\Client\Model\Shop\Response\Core;

use Incert\Client\AbstractModel;

/**
 * Language Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Core
 * @implements \ArrayAccess<string, mixed>
 */
class Lang extends AbstractModel
{
    protected $data = [
        'name' => null,
        'code' => null,
    ];

    public function getName(): mixed
    {
        return $this->data['name'];
    }

    public function setName(mixed $name)
    {
        $this->data['name'] = $name;
        return $this;
    }

    public function getCode(): mixed
    {
        return $this->data['code'];
    }

    public function setCode(mixed $code)
    {
        $this->data['code'] = $code;
        return $this;
    }
}
