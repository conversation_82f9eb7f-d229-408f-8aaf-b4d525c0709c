<?php
/**
 * Validate
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 */

namespace Incert\Client\Model\Shop\Response\Order;

use Incert\Client\AbstractModel;

/**
 * Validate Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 * @implements \ArrayAccess<string, mixed>
 */
class Validate extends AbstractModel
{
    protected $data = [
        'canProcess' => null,
        'total' => null,
        'totalWithoutDiscount' => null,
        'discountMade' => null,
        'taxClasses' => [],
        'taxGroups' => [],
        'taxTotal' => null,
        'shipping' => [],
        'currency' => null,
    ];

    public function getCanProcess(): mixed
    {
        return $this->data['canProcess'];
    }

    public function setCanProcess(mixed $canProcess)
    {
        $this->data['canProcess'] = $canProcess;
        return $this;
    }

    public function getTotal(): mixed
    {
        return $this->data['total'];
    }

    public function setTotal(mixed $total)
    {
        $this->data['total'] = $total;
        return $this;
    }

    public function getTotalWithoutDiscount(): mixed
    {
        return $this->data['totalWithoutDiscount'];
    }

    public function setTotalWithoutDiscount(mixed $totalWithoutDiscount)
    {
        $this->data['totalWithoutDiscount'] = $totalWithoutDiscount;
        return $this;
    }

    public function getDiscountMade(): mixed
    {
        return $this->data['discountMade'];
    }

    public function setDiscountMade(mixed $discountMade)
    {
        $this->data['discountMade'] = $discountMade;
        return $this;
    }

    public function getTaxClasses(): array
    {
        return $this->data['taxClasses'];
    }

    public function setTaxClasses(array $taxClasses)
    {
        $this->data['taxClasses'] = $taxClasses;
        return $this;
    }

    public function getTaxGroups(): array
    {
        return $this->data['taxGroups'];
    }

    public function setTaxGroups(array $taxGroups)
    {
        $this->data['taxGroups'] = $taxGroups;
        return $this;
    }

    public function getTaxTotal(): mixed
    {
        return $this->data['taxTotal'];
    }

    public function setTaxTotal(mixed $taxTotal)
    {
        $this->data['taxTotal'] = $taxTotal;
        return $this;
    }

    public function getShipping(): mixed
    {
        return $this->data['shipping'];
    }

    public function setShipping(array $shipping)
    {
        $this->data['shipping'] = $shipping;
        return $this;
    }

    public function getCurrency(): mixed
    {
        return $this->data['currency'];
    }

    public function setCurrency(mixed $currency)
    {
        $this->data['currency'] = $currency;
        return $this;
    }
}
