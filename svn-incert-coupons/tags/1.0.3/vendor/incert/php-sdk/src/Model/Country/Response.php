<?php

namespace Incert\Client\Model\Country;

use Incert\Client\AbstractModel;

class Response extends AbstractModel
{
    protected $data = [
        'name'     => null,
        'isoCode2' => null,
        'id'       => null,
        'isoCode3' => null,
        'status'   => null,
    ];

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->data['name'];
    }

    /**
     * @param mixed $name
     * @return Response
     */
    public function setName($name)
    {
        $this->data['name'] = $name;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsoCode2()
    {
        return $this->data['isoCode2'];
    }

    /**
     * @param mixed $isoCode2
     * @return Response
     */
    public function setIsoCode2($isoCode2)
    {
        $this->data['isoCode2'] = $isoCode2;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->data['id'];
    }

    /**
     * @param mixed $id
     * @return Response
     */
    public function setId($id)
    {
        $this->data['id'] = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIsoCode3()
    {
        return $this->data['isoCode3'];
    }

    /**
     * @param mixed $isoCode3
     * @return Response
     */
    public function setIsoCode3($isoCode3)
    {
        $this->data['isoCode3'] = $isoCode3;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->data['status'];
    }

    /**
     * @param mixed $status
     * @return Response
     */
    public function setStatus($status)
    {
        $this->data['status'] = $status;
        return $this;
    }

}