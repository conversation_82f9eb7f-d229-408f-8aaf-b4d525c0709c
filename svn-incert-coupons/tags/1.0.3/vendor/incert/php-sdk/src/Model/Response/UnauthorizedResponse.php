<?php
/**
 * UnauthorizedResponse
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Response;

use Incert\Client\AbstractModel;

/**
 * UnauthorizedResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class UnauthorizedResponse extends AbstractModel
{

    protected $data = [
        'error'            => null,
        'error_description' => null,
        'hint'             => null,
        'message'          => null,
    ];

    public function getError(): mixed
    {
        return $this->data['error'];
    }

    public function setError(mixed $error)
    {
        $this->data['error'] = $error;
        return $this;
    }

    public function getErrorDescription(): mixed
    {
        return $this->data['error_description'];
    }

    public function setErrorDescription(mixed $errorDescription)
    {
        $this->data['error_description'] = $errorDescription;
        return $this;
    }

    public function getHint(): mixed
    {
        return $this->data['hint'];
    }

    public function setHint(mixed $hint)
    {
        $this->data['hint'] = $hint;
        return $this;
    }

    public function getMessage(): mixed
    {
        return $this->data['message'];
    }

    public function setMessage(mixed $message)
    {
        $this->data['message'] = $message;
        return $this;
    }

}


