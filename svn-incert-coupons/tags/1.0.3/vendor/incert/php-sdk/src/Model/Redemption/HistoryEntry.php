<?php
/**
 * IncertCommonApiModelRedemptionHistoryEntry
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redemption;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedemptionHistoryEntry Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class HistoryEntry extends AbstractModel
{

    protected $data = [
        'redemptionID'          => null,
        'redemptionDate'        => null,
        'redemptionValue'       => null,
        'redemptionComment'     => null,
        'redemptionStationID'   => null,
        'redemptionStationName' => null,
        'bookingID'             => null,
        'inclusiveName'         => null,
        'inclusiveID'           => null,
    ];

    public function getRedemptionID(): mixed
    {
        return $this->data['redemptionID'];
    }

    public function setRedemptionID(mixed $redemptionID)
    {
        $this->data['redemptionID'] = $redemptionID;
        return $this;
    }

    public function getRedemptionDate(): mixed
    {
        return $this->data['redemptionDate'];
    }

    public function setRedemptionDate(mixed $redemptionDate)
    {
        $this->data['redemptionDate'] = $redemptionDate;
        return $this;
    }

    public function getRedemptionComment(): mixed
    {
        return $this->data['redemptionComment'];
    }

    public function setRedemptionComment(mixed $redemptionComment)
    {
        $this->data['redemptionComment'] = $redemptionComment;
        return $this;
    }

    public function getRedemptionValue(): mixed
    {
        return $this->data['redemptionValue'];
    }

    public function setRedemptionValue(mixed $redemptionValue)
    {
        $this->data['redemptionValue'] = $redemptionValue;
        return $this;
    }

    public function getRedemptionStationID(): mixed
    {
        return $this->data['redemptionStationID'];
    }

    public function setRedemptionStationID(mixed $redemptionStationID)
    {
        $this->data['redemptionStationID'] = $redemptionStationID;
        return $this;
    }

    public function getRedemptionStationName(): mixed
    {
        return $this->data['redemptionStationName'];
    }

    public function setRedemptionStationName(mixed $redemptionStationName)
    {
        $this->data['redemptionStationName'] = $redemptionStationName;
        return $this;
    }

    public function getBookingID(): mixed
    {
        return $this->data['bookingID'];
    }

    public function setBookingID(mixed $bookingID)
    {
        $this->data['bookingID'] = $bookingID;
        return $this;
    }

    public function getInclusiveName(): mixed
    {
        return $this->data['inclusiveName'];
    }

    public function setInclusiveName(mixed $inclusiveName)
    {
        $this->data['inclusiveName'] = $inclusiveName;
        return $this;
    }

    public function getInclusiveID(): mixed
    {
        return $this->data['inclusiveID'];
    }

    public function setInclusiveID(mixed $inclusiveID)
    {
        $this->data['inclusiveID'] = $inclusiveID;
        return $this;
    }

}


