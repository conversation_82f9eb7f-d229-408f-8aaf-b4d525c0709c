<?php
/**
 * IncertCommonApiModelRedemptionUnit
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redemption;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedemptionUnit Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class Unit extends AbstractModel
{
    protected $data = [
        'redUnitID'     => null,
        'redUnitName'   => null,
        'redUnitAmount' => null,
        'taxRate'       => null,
    ];

    public function getRedUnitID(): mixed
    {
        return $this->data['redUnitID'];
    }

    public function setRedUnitID(mixed $redUnitID)
    {
        $this->data['redUnitID'] = $redUnitID;
        return $this;
    }

    public function getRedUnitName(): mixed
    {
        return $this->data['redUnitName'];
    }

    public function setRedUnitName(mixed $redUnitName)
    {
        $this->data['redUnitName'] = $redUnitName;
        return $this;
    }

    public function getRedUnitAmount(): mixed
    {
        return $this->data['redUnitAmount'];
    }

    public function setRedUnitAmount(mixed $redUnitAmount)
    {
        $this->data['redUnitAmount'] = $redUnitAmount;
        return $this;
    }

    public function getTaxRate(): mixed
    {
        return $this->data['taxRate'];
    }

    public function setTaxRate(mixed $taxRate)
    {
        $this->data['taxRate'] = $taxRate;
        return $this;
    }

}


