<?php
/**
 * IncertCommonApiModelBonusPointChargeHistoryEntry
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelBonusPointChargeHistoryEntry Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class BonusPointChargeHistoryEntry extends AbstractModel
{

    protected $data = [
        'id'        => null,
        'code'      => null,
        'date'      => null,
        'points'    => null,
        'stationID' => null,
    ];

    /**
     * @return mixed|null
     */
    public function getId()
    {
        return $this->data['id'];
    }

    /**
     * @param mixed|null $id
     */
    public function setId($id)
    {
        $this->data['id'] = $id;
        return $this;
    }

    /**
     * @return mixed|null
     */
    public function getCode()
    {
        return $this->data['code'];
    }

    /**
     * @param mixed|null $code
     */
    public function setCode($code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    /**
     * @return mixed|null
     */
    public function getDate()
    {
        return $this->data['date'];
    }

    /**
     * @param mixed|null $date
     */
    public function setDate($date)
    {
        $this->data['date'] = $date;
        return $this;
    }

    /**
     * @return mixed|null
     */
    public function getPoints()
    {
        return $this->data['points'];
    }

    /**
     * @param mixed|null $points
     */
    public function setPoints($points)
    {
        $this->data['points'] = $points;
        return $this;
    }

    /**
     * @return mixed|null
     */
    public function getStationID()
    {
        return $this->data['stationID'];
    }

    /**
     * @param mixed|null $stationID
     */
    public function setStationID($stationID)
    {
        $this->data['stationID'] = $stationID;
        return $this;
    }
}


