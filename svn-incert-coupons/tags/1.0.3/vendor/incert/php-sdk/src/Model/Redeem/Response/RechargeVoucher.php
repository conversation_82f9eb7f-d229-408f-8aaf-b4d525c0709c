<?php

namespace Incert\Client\Model\Redeem\Response;

use Incert\Client\AbstractModel;

class RechargeVoucher extends AbstractModel
{

    protected $data = [
        'rechargeID' => null,
        'rechargedAmount' => null,
        'currentAmount' => null,
    ];

    public function getRechargeId ()
    {
        return $this->data['rechargeId'];
    }

    public function setRechargeId ($rechargeId)
    {
        $this->data['rechargeId'] = $rechargeId;
        return $this;
    }

    public function getRechargedAmount ()
    {
        return $this->data['rechargedAmount'];
    }

    public function setRechargedAmount ($rechargeAmount)
    {
        $this->data['rechargedAmount'] = $rechargeAmount;
        return $this;
    }

    public function getCurrentAmount ()
    {
        return $this->data['currentAmount'];
    }

    public function setCurrentAmount ($currentAmount)
    {
        $this->data['currentAmount'] = $currentAmount;
        return $this;
    }

}