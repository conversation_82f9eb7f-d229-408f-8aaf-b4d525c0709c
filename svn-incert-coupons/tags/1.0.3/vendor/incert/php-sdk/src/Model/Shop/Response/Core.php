<?php
/**
 * Core
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response
 */

namespace Incert\Client\Model\Shop\Response;

use Incert\Client\AbstractModel;
use Incert\Client\Model\Shop\Response\Core\Base;

/**
 * Core Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response
 * @implements \ArrayAccess<string, mixed>
 */
class Core extends AbstractModel
{
    protected $data = [
        'shipping' => null,
        'payment' => null,
    ];

    /**
     * @return mixed | Base
     */
    public function getShipping(): mixed
    {
        return $this->data['shipping'];
    }

    public function setShipping(mixed $shippingMethods)
    {
        if (is_array($shippingMethods)) {
            $data = [];
            foreach($shippingMethods as $shipping){
                $data []= new Base($shipping);
            }
            $shippingMethods = $data;
        }
        $this->data['shipping'] = $shippingMethods;
        return $this;
    }

    /**
     * @return mixed | Base
     */
    public function getPayment(): mixed
    {
        return $this->data['payment'];
    }

    public function setPayment(mixed $paymentMethods)
    {
        if (is_array($paymentMethods)) {
            $data = [];
            foreach($paymentMethods as $payment){
                $data []= new Base($payment);
            }
            $paymentMethods = $data;
        }
        $this->data['payment'] = $paymentMethods;
        return $this;
    }
}
