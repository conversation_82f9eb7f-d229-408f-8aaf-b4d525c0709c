<?php
/**
 * Cancel
 * PHP version 7.4+
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 */

namespace Incert\Client\Model\Shop\Response\Order;

use Incert\Client\AbstractModel;

/**
 * Cancel Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 * @implements \ArrayAccess<string, mixed>
 */
class Cancel extends AbstractModel
{
    protected $data = [
        'orderId' => null,
        'responseMsg' => null
    ];

    public function getOrderId(): mixed
    {
        return $this->data['orderId'];
    }

    public function setOrderId(mixed $orderId)
    {
        $this->data['orderId'] = $orderId;
        return $this;
    }

    public function getResponseMsg(): mixed
    {
        return $this->data['responseMsg'];
    }

    public function setResponseMsg(mixed $responseMsg)
    {
        $this->data['responseMsg'] = $responseMsg;
        return $this;
    }
}
