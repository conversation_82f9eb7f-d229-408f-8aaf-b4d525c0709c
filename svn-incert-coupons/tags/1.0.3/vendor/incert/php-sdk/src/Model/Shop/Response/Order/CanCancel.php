<?php
/**
 * CanCancel
 * PHP version 7.4+
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 */

namespace Incert\Client\Model\Shop\Response\Order;

use Incert\Client\AbstractModel;

/**
 * CanCancel Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 * @implements \ArrayAccess<string, mixed>
 */
class CanCancel extends AbstractModel
{
    protected $data = [
        'fullCancel' => null,
        'products' => null
    ];

    public function getCanFullCancel(): mixed
    {
        return $this->data['fullCancel'];
    }

    public function setCanFullCancel(mixed $fullCancel)
    {
        $this->data['fullCancel'] = $fullCancel;
        return $this;
    }

    public function getProducts(): mixed
    {
        return $this->data['products'];
    }

    public function setProducts(mixed $products)
    {
        $this->data['products'] = $products;
        return $this;
    }
}
