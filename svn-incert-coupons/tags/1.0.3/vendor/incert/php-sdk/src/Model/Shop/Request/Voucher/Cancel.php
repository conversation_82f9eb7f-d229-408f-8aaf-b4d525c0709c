<?php
/**
 * Cancel
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Voucher
 */

namespace Incert\Client\Model\Shop\Request\Voucher;

use Incert\Client\AbstractModel;

/**
 * Cancel Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Voucher
 * @implements \ArrayAccess<string, mixed>
 */
class Cancel extends AbstractModel
{
    protected $data = [
        'comment' => "",
        'voucherCode' => ""
    ];

    public function getComment(): string
    {
        return $this->data['comment'];
    }

    public function setComment($comment)
    {
        $this->data['comment'] = $comment;
        return $this;
    }

    public function getVoucherCode(): string
    {
        return $this->data['voucherCode'];
    }

    public function setVoucherCode($voucherCode)
    {
        $this->data['voucherCode'] = $voucherCode;
        return $this;
    }
}
