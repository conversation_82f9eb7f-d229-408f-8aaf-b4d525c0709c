<?php
/**
 * IncertCommonApiModelRedeemBookingRestriction
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemBookingRestriction Class Doc Comment
 * @category Class
 * @package  Incert\Client
 * @implements \ArrayAccess<string, mixed>
 */
class BookingRestriction extends AbstractModel
{
    protected $data = [
        'bookingOnlyRackRates'    => null,
        'bookingStartDate'        => null,
        'bookingEndDate'          => null,
        'reservationStartDate'    => null,
        'reservationEndDate'      => null,
        'allowedRedeemHotels'     => null,
        'reservationExcludeDates' => null,
    ];

    public function getBookingOnlyRackRates(): mixed
    {
        return $this->data['bookingOnlyRackRates'];
    }

    public function setBookingOnlyRackRates(mixed $bookingOnlyRackRates)
    {
        $this->data['bookingOnlyRackRates'] = $bookingOnlyRackRates;
        return $this;
    }

    public function getBookingStartDate(): mixed
    {
        return $this->data['bookingStartDate'];
    }

    public function setBookingStartDate(mixed $bookingStartDate)
    {
        $this->data['bookingStartDate'] = $bookingStartDate;
        return $this;
    }

    public function getBookingEndDate(): mixed
    {
        return $this->data['bookingEndDate'];
    }

    public function setBookingEndDate(mixed $bookingEndDate)
    {
        $this->data['bookingEndDate'] = $bookingEndDate;
        return $this;
    }

    public function getReservationStartDate(): mixed
    {
        return $this->data['reservationStartDate'];
    }

    public function setReservationStartDate(mixed $reservationStartDate)
    {
        $this->data['reservationStartDate'] = $reservationStartDate;
        return $this;
    }

    public function getReservationEndDate(): mixed
    {
        return $this->data['reservationEndDate'];
    }

    public function setReservationEndDate(mixed $reservationEndDate)
    {
        $this->data['reservationEndDate'] = $reservationEndDate;
        return $this;
    }

    public function getAllowedRedeemHotels(): mixed
    {
        return $this->data['allowedRedeemHotels'];
    }

    public function setAllowedRedeemHotels(mixed $allowedRedeemHotels)
    {
        $this->data['allowedRedeemHotels'] = $allowedRedeemHotels;
        return $this;
    }

    public function getReservationExcludeDates(): mixed
    {
        return $this->data['reservationExcludeDates'];
    }

    public function setReservationExcludeDates(mixed $reservationExcludeDates)
    {
        $this->data['reservationExcludeDates'] = $reservationExcludeDates;
        return $this;
    }

}


