<?php
/**
 * Product
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\
 */

namespace Incert\Client\Model\Shop\Response;

use Incert\Client\AbstractModel;

/**
 * Product Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\
 * @implements \ArrayAccess<string, mixed>
 */
class Product extends AbstractModel
{
    protected $data = [
        'id' => null,
        'thirdPartyId' => null,
        'type' => null,
        'taxRate' => null,
        'quantity' => null,
        'checkStock' => null,
        'price' => null,
        'grossPrice' => null,
        'grossPriceFormatted' => null,
        'articleMinPrice' => null,
        'currency' => null,
        'manufacturerId' => null,
        'sort' => null,
        'isDiscount' => null,
        'printManInfo' => null,
        'imageLink' => null,
        'thumbnailImageLink' => null,
        'productDescriptions' => [],
        'categories' => [],
        'isParticipantAssignable' => null,
        'masterProducts' => [],
        'assignedProducts' => [],
        'parentProducts' => [],
        'childProducts' => [],
        'articleMaxPrice' => null,
    ];

    public function getId(): mixed
    {
        return $this->data['id'];
    }

    public function setId(mixed $id)
    {
        $this->data['id'] = $id;
        return $this;
    }

    public function getThirdPartyId(): mixed
    {
        return $this->data['thirdPartyId'];
    }

    public function setThirdPartyId(mixed $thirdPartyId)
    {
        $this->data['thirdPartyId'] = $thirdPartyId;
        return $this;
    }

    public function getType(): mixed
    {
        return $this->data['type'];
    }

    public function setType(mixed $type)
    {
        $this->data['type'] = $type;
        return $this;
    }

    public function getTaxRate(): mixed
    {
        return $this->data['taxRate'];
    }

    public function setTaxRate(mixed $taxRate)
    {
        $this->data['taxRate'] = $taxRate;
        return $this;
    }

    public function getQuantity(): mixed
    {
        return $this->data['quantity'];
    }

    public function setQuantity(mixed $quantity)
    {
        $this->data['quantity'] = $quantity;
        return $this;
    }

    public function getCheckStock(): mixed
    {
        return $this->data['checkStock'];
    }

    public function setCheckStock(mixed $checkStock)
    {
        $this->data['checkStock'] = $checkStock;
        return $this;
    }

    public function getPrice(): mixed
    {
        return $this->data['price'];
    }

    public function setPrice(mixed $price)
    {
        $this->data['price'] = $price;
        return $this;
    }

    public function getGrossPrice(): mixed
    {
        return $this->data['grossPrice'];
    }

    public function setGrossPrice(mixed $grossPrice)
    {
        $this->data['grossPrice'] = $grossPrice;
        return $this;
    }

    public function getGrossPriceFormatted(): mixed
    {
        return $this->data['grossPriceFormatted'];
    }

    public function setGrossPriceFormatted(mixed $grossPriceFormatted)
    {
        $this->data['grossPriceFormatted'] = $grossPriceFormatted;
        return $this;
    }

    public function getArticleMinPrice(): mixed
    {
        return $this->data['articleMinPrice'];
    }

    public function setArticleMinPrice(mixed $articleMinPrice)
    {
        $this->data['articleMinPrice'] = $articleMinPrice;
        return $this;
    }

    public function getCurrency(): mixed
    {
        return $this->data['currency'];
    }

    public function setCurrency(mixed $currency)
    {
        $this->data['currency'] = $currency;
        return $this;
    }

    public function getManufacturerId(): mixed
    {
        return $this->data['manufacturerId'];
    }

    public function setManufacturerId(mixed $manufacturerId)
    {
        $this->data['manufacturerId'] = $manufacturerId;
        return $this;
    }

    public function getSort(): mixed
    {
        return $this->data['sort'];
    }

    public function setSort(mixed $sort)
    {
        $this->data['sort'] = $sort;
        return $this;
    }

    public function getIsDiscount(): mixed
    {
        return $this->data['isDiscount'];
    }

    public function setIsDiscount(mixed $isDiscount)
    {
        $this->data['isDiscount'] = $isDiscount;
        return $this;
    }

    public function getPrintManInfo(): mixed
    {
        return $this->data['printManInfo'];
    }

    public function setPrintManInfo(mixed $printManInfo)
    {
        $this->data['printManInfo'] = $printManInfo;
        return $this;
    }

    public function getImageLink(): mixed
    {
        return $this->data['imageLink'];
    }

    public function setImageLink(mixed $imageLink)
    {
        $this->data['imageLink'] = $imageLink;
        return $this;
    }

    public function getThumbnailImageLink(): mixed
    {
        return $this->data['thumbnailImageLink'];
    }

    public function setThumbnailImageLink(mixed $thumbnailImageLink)
    {
        $this->data['thumbnailImageLink'] = $thumbnailImageLink;
        return $this;
    }

    public function getProductDescriptions(): array
    {
        return $this->data['productDescriptions'];
    }

    public function setProductDescriptions(array $productDescriptions)
    {
        $this->data['productDescriptions'] = $productDescriptions;
        return $this;
    }

    public function getCategories(): array
    {
        return $this->data['categories'];
    }

    public function setCategories(array $categories)
    {
        $this->data['categories'] = $categories;
        return $this;
    }

    public function getIsParticipantAssignable(): mixed
    {
        return $this->data['isParticipantAssignable'];
    }

    public function setIsParticipantAssignable(mixed $isParticipantAssignable)
    {
        $this->data['isParticipantAssignable'] = $isParticipantAssignable;
        return $this;
    }

    public function getMasterProducts(): array
    {
        return $this->data['masterProducts'];
    }

    public function setMasterProducts(array $masterProducts)
    {
        $this->data['masterProducts'] = $masterProducts;
        return $this;
    }

    public function getAssignedProducts(): array
    {
        return $this->data['assignedProducts'];
    }

    public function setAssignedProducts(array $assignedProducts)
    {
        $this->data['assignedProducts'] = $assignedProducts;
        return $this;
    }

    public function getParentProducts(): array
    {
        return $this->data['parentProducts'];
    }

    public function setParentProducts(array $parentProducts)
    {
        $this->data['parentProducts'] = $parentProducts;
        return $this;
    }

    public function getChildProducts(): array
    {
        return $this->data['childProducts'];
    }

    public function setChildProducts(array $childProducts)
    {
        $this->data['childProducts'] = $childProducts;
        return $this;
    }

    public function getArticleMaxPrice(): mixed
    {
        return $this->data['articleMaxPrice'];
    }

    public function setArticleMaxPrice(mixed $articleMaxPrice)
    {
        $this->data['articleMaxPrice'] = $articleMaxPrice;
        return $this;
    }
}
