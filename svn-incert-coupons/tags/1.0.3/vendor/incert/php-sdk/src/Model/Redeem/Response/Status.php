<?php
/**
 * IncertCommonApiModelRedeemStatusResponse
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem\Response;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemStatusResponse Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class Status extends AbstractModel
{

    protected $data = [
        'code'   => null,
        'status' => null,
    ];

    public function getCode(): mixed
    {
        return $this->data['code'];
    }

    public function setCode(mixed $code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    public function getStatus(): mixed
    {
        return $this->data['status'];
    }

    public function setStatus(mixed $status)
    {
        $this->data['status'] = $status;
        return $this;
    }

}


