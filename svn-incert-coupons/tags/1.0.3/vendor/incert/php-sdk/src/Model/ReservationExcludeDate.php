<?php
/**
 * IncertCommonApiModelReservationExcludeDate
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelReservationExcludeDate Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class ReservationExcludeDate extends AbstractModel
{

    protected $data = [
        'wcbedID'   => null,
        'winCampID' => null,
        'start'     => null,
        'end'       => null,
    ];

    public function getWcbedID(): mixed
    {
        return $this->data['wcbedID'];
    }

    public function setWcbedID(mixed $wcbedID)
    {
        $this->data['wcbedID'] = $wcbedID;
        return $this;
    }

    public function getWinCampID(): mixed
    {
        return $this->data['winCampID'];
    }

    public function setWinCampID(mixed $winCampID)
    {
        $this->data['winCampID'] = $winCampID;
        return $this;
    }

    public function getStart(): mixed
    {
        return $this->data['start'];
    }

    public function setStart(mixed $start)
    {
        $this->data['start'] = $start;
        return $this;
    }

    public function getEnd(): mixed
    {
        return $this->data['end'];
    }

    public function setEnd(mixed $end)
    {
        $this->data['end'] = $end;
        return $this;
    }

}


