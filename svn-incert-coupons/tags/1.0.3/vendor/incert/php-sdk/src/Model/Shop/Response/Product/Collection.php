<?php

namespace Incert\Client\Model\Shop\Response\Product;

use Incert\Client\AbstractModel;
use Incert\Client\Model\Shop\Response\Product;

class Collection extends AbstractModel
{
    public function __construct($data = [])
    {
        $products = [];
        foreach ($data as $product) {
            $products []= new Product($product);
        }
        $this->data['products'] = $products;
    }

    /**
     * @return Product[]
     */
    public function getProducts()
    {
        return $this->data['products'];
    }

}