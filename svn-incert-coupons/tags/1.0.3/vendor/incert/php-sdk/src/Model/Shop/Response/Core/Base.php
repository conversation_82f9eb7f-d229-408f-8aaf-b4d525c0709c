<?php
/**
 * Base
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Core
 */

namespace Incert\Client\Model\Shop\Response\Core;

use Incert\Client\AbstractModel;

/**
 * Base Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Core
 * @implements \ArrayAccess<string, mixed>
 */
class Base extends AbstractModel
{
    protected $data = [
        'module' => null,
        'title' => null,
        'shortDescription' => null,
        'description' => null,
        'lang' => null
    ];

    public function getModule(): mixed
    {
        return $this->data['module'];
    }

    public function setModule(mixed $module)
    {
        $this->data['module'] = $module;
        return $this;
    }

    public function getTitle(): mixed
    {
        return $this->data['title'];
    }

    public function setTitle(mixed $title)
    {
        $this->data['title'] = $title;
        return $this;
    }

    public function getShortDescription(): mixed
    {
        return $this->data['shortDescription'];
    }

    public function setShortDescription(mixed $shortDescription)
    {
        $this->data['shortDescription'] = $shortDescription;
        return $this;
    }

    public function getDescription(): mixed
    {
        return $this->data['description'];
    }

    public function setDescription(mixed $description)
    {
        $this->data['description'] = $description;
        return $this;
    }

    /**
     * @return mixed | Lang
     */
    public function getLang(): mixed
    {
        return $this->data['lang'];
    }

    public function setLang(mixed $language)
    {
        if(is_array($language)){
            $language = new Lang($language);
        }
        $this->data['lang'] = $language;
        return $this;
    }
}
