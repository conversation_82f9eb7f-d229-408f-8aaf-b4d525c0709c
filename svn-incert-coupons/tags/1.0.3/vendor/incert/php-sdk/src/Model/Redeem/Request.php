<?php
/**
 * IncertCommonApiModelRedeemRequest
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client
 */

namespace Incert\Client\Model\Redeem;

use Incert\Client\AbstractModel;

/**
 * IncertCommonApiModelRedeemRequest Class Doc Comment
 * @category Class
 * @package  Incert\Client
 */
class Request extends AbstractModel
{
    protected $data = [
        'code'               => null,
        'amount'             => null,
        'currency'           => null,
        'bookingID'          => null,
        'bookingPartnerID'   => null,
        'bookingFromDate'    => null,
        'bookingToDate'      => null,
        'bookingTotalAmount' => null,
        'comment'            => null,
        'emailAddress'       => null,
        'pin'                => null,
        'attributeModel'     => null,
        'valueScaleID'       => null,
    ];

    public function getCode(): mixed
    {
        return $this->data['code'];
    }

    public function setCode(mixed $code)
    {
        $this->data['code'] = $code;
        return $this;
    }

    public function getAmount(): mixed
    {
        return $this->data['amount'];
    }

    public function setAmount(mixed $amount)
    {
        $this->data['amount'] = $amount;
        return $this;
    }

    public function getCurrency(): mixed
    {
        return $this->data['currency'];
    }

    public function setCurrency(mixed $currency)
    {
        $this->data['currency'] = $currency;
        return $this;
    }

    public function getBookingID(): mixed
    {
        return $this->data['bookingID'];
    }

    public function setBookingID(mixed $bookingID)
    {
        $this->data['bookingID'] = $bookingID;
        return $this;
    }

    public function getBookingPartnerID(): mixed
    {
        return $this->data['bookingPartnerID'];
    }

    public function setBookingPartnerID(mixed $bookingPartnerID)
    {
        $this->data['bookingPartnerID'] = $bookingPartnerID;
        return $this;
    }

    public function getBookingFromDate(): mixed
    {
        return $this->data['bookingFromDate'];
    }

    public function setBookingFromDate(mixed $bookingFromDate)
    {
        $this->data['bookingFromDate'] = $bookingFromDate;
        return $this;
    }

    public function getBookingToDate(): mixed
    {
        return $this->data['bookingToDate'];
    }

    public function setBookingToDate(mixed $bookingToDate)
    {
        $this->data['bookingToDate'] = $bookingToDate;
        return $this;
    }

    public function getBookingTotalAmount(): mixed
    {
        return $this->data['bookingTotalAmount'];
    }

    public function setBookingTotalAmount(mixed $bookingTotalAmount)
    {
        $this->data['bookingTotalAmount'] = $bookingTotalAmount;
        return $this;
    }

    public function getComment(): mixed
    {
        return $this->data['comment'];
    }

    public function setComment(mixed $comment)
    {
        $this->data['comment'] = $comment;
        return $this;
    }

    public function getEmailAddress(): mixed
    {
        return $this->data['emailAddress'];
    }

    public function setEmailAddress(mixed $emailAddress)
    {
        $this->data['emailAddress'] = $emailAddress;
        return $this;
    }

    public function getPin(): mixed
    {
        return $this->data['pin'];
    }

    public function setPin(mixed $pin)
    {
        $this->data['pin'] = $pin;
        return $this;
    }

    public function getAttributeModel(): mixed
    {
        return $this->data['attributeModel'];
    }

    public function setAttributeModel(mixed $attributeModel)
    {
        $this->data['attributeModel'] = $attributeModel;
        return $this;
    }

    public function getValueScaleID(): mixed
    {
        return $this->data['valueScaleID'];
    }

    public function setValueScaleID(mixed $valueScaleID)
    {
        $this->data['valueScaleID'] = $valueScaleID;
        return $this;
    }

}


