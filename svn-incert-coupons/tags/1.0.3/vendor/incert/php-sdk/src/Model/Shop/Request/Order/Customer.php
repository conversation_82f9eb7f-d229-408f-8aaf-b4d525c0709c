<?php
/**
 * Customer
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order
 */

namespace Incert\Client\Model\Shop\Request\Order;

use Incert\Client\AbstractModel;
use Incert\Client\Model\Shop\Request\Order\Customer\Address;

/**
 * Customer Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Request\Order
 * @implements \ArrayAccess<string, mixed>
 */
class Customer extends AbstractModel
{
    protected $data = [
        'cid' => null,
        'email' => null,
        'firstName' => null,
        'lastName' => null,
        'gender' => null,
        'dob' => null,
        'phoneNumber' => null,
        'language' => null,
        'newsletter' => null,
        'shippingAddress' => [],
        'paymentAddress' => [],
    ];

    public function getCid(): ?string
    {
        return $this->data['cid'];
    }

    public function setCid(?string $cid): self
    {
        $this->data['cid'] = $cid;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->data['email'];
    }

    public function setEmail(?string $email): self
    {
        $this->data['email'] = $email;
        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->data['firstName'];
    }

    public function setFirstName(?string $firstName): self
    {
        $this->data['firstName'] = $firstName;
        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->data['lastName'];
    }

    public function setLastName(?string $lastName): self
    {
        $this->data['lastName'] = $lastName;
        return $this;
    }

    public function getGender(): ?string
    {
        return $this->data['gender'];
    }

    public function setGender(?string $gender): self
    {
        $this->data['gender'] = $gender;
        return $this;
    }

    public function getDob(): ?string
    {
        return $this->data['dob'];
    }

    public function setDob(?string $dob): self
    {
        $this->data['dob'] = $dob;
        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->data['phoneNumber'];
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->data['phoneNumber'] = $phoneNumber;
        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->data['language'];
    }

    public function setLanguage(?string $language): self
    {
        $this->data['language'] = $language;
        return $this;
    }

    public function isNewsletter(): ?bool
    {
        return $this->data['newsletter'];
    }

    public function setNewsletter(?bool $newsletter): self
    {
        $this->data['newsletter'] = $newsletter;
        return $this;
    }

    public function getShippingAddress(): array
    {
        return $this->data['shippingAddress'];
    }

    public function setShippingAddress(array $shippingAddress): self
    {
        $this->data['shippingAddress'] = new Address($shippingAddress);
        return $this;
    }

    public function getPaymentAddress(): array
    {
        return $this->data['paymentAddress'];
    }

    public function setPaymentAddress(array $paymentAddress): self
    {
        $this->data['paymentAddress'] = new Address($paymentAddress);
        return $this;
    }
}
