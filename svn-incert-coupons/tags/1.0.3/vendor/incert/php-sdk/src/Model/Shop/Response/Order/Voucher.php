<?php
/**
 * Voucher
 * PHP version 7.4
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 */

namespace Incert\Client\Model\Shop\Response\Order;

use Incert\Client\AbstractModel;

/**
 * Voucher Class Doc Comment
 * @category Class
 * @package  Incert\Client\Model\Shop\Response\Order
 * @implements \ArrayAccess<string, mixed>
 */
class Voucher extends AbstractModel
{
    protected $data = [
        'productId' => null,
        'voucherCode' => null,
        'voucherValue' => null,
        'validUntil' => null,
        'itemId' => null,
    ];

    public function getProductId(): mixed
    {
        return $this->data['productId'];
    }

    public function setProductId(mixed $productId)
    {
        $this->data['productId'] = $productId;
        return $this;
    }

    public function getVoucherCode(): mixed
    {
        return $this->data['voucherCode'];
    }

    public function setVoucherCode(mixed $voucherCode)
    {
        $this->data['voucherCode'] = $voucherCode;
        return $this;
    }

    public function getVoucherValue(): mixed
    {
        return $this->data['voucherValue'];
    }

    public function setVoucherValue(mixed $voucherValue)
    {
        $this->data['voucherValue'] = $voucherValue;
        return $this;
    }

    public function getValidUntil(): mixed
    {
        return $this->data['validUntil'];
    }

    public function setValidUntil(mixed $validUntil)
    {
        $this->data['validUntil'] = $validUntil;
        return $this;
    }

    public function getItemId(): mixed
    {
        return $this->data['itemId'];
    }

    public function setItemId(mixed $itemId)
    {
        $this->data['itemId'] = $itemId;
        return $this;
    }
}
