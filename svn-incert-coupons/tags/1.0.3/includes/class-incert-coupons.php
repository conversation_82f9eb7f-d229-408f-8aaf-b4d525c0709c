<?php

/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @link       https://
 * @since      1.0.0
 *
 * @package    Incert_Voucher_Handling
 * @subpackage Incert_Voucher_Handling/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 *
 * @since      1.0.0
 * @package    Incert_Voucher_Handling
 * @subpackage Incert_Voucher_Handling/includes
 * <AUTHOR> eTourismus GmbH & Co KG <<EMAIL>>
 */
class Incert_Coupons
{

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Incert_Coupons_Loader $loader Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string $plugin_name The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string $version The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * Set the plugin name and the plugin version that can be used throughout the plugin.
     * Load the dependencies, define the locale, and set the hooks for the admin area and
     * the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function __construct()
    {
        if (defined('INCERT_COUPONS_VERSION')) {
            $this->version = INCERT_COUPONS_VERSION;
        } else {
            $this->version = '1.0.0';
        }
        $this->plugin_name = 'incert-coupons';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();

        /* include sdk */
        require_once(__DIR__ . '/../vendor/autoload.php');
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * Include the following files that make up the plugin:
     *
     * - Incert_Coupons_Loader. Orchestrates the hooks of the plugin.
     * - Incert_Coupons_i18n. Defines internationalization functionality.
     * - Incert_Coupons_Admin. Defines all hooks for the admin area.
     * - Incert_Coupons_Public. Defines all hooks for the public side of the site.
     *
     * Create an instance of the loader which will be used to register the hooks
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies()
    {

        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-incert-coupons-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-incert-coupons-i18n.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/class-incert-coupons-admin.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'public/class-incert-coupons-public.php';

        $this->loader = new Incert_Coupons_Loader();

    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the Incert_Coupons_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale()
    {

        $plugin_i18n = new Incert_Coupons_i18n();

        $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');

    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks()
    {
        $plugin_admin = new Incert_Coupons_Admin($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');

        add_action('admin_menu', [$this, 'custom_admin_menu']);
        add_filter('manage_edit-shop_coupon_columns', [$this, 'custom_add_coupon_column']);
        add_action('manage_shop_coupon_posts_custom_column', [$this, 'custom_coupon_column_data'], 10, 2);
        add_action('load-post.php', [$this, 'custom_coupon_editing_restrictions']);
        add_action('woocommerce_product_options_general_product_data', [$this, 'custom_product_add_incert_coupon_dropdown']);
        add_action('woocommerce_process_product_meta', [$this, 'custom_product_save_incert_coupon_dropdown']);
        add_action('woocommerce_admin_order_data_after_order_details', [$this, 'custom_order_details_data']);
        add_action('woocommerce_update_product', [$this, 'custom_product_save_action'], 10, 1);

        add_filter('manage_woocommerce_page_wc-orders_columns', [$this, 'custom_orders_new_column']);
        add_action('manage_woocommerce_page_wc-orders_custom_column', [$this, 'custom_orders_new_column_content'], 25, 2 );

        add_action( 'woocommerce_order_list_table_restrict_manage_orders', [$this, 'custom_order_filter'], 25, 2 );
        add_action( 'restrict_manage_posts', [$this, 'custom_order_filter'], 25, 2 );
        add_action( 'woocommerce_order_list_table_prepare_items_query_args', [$this, 'custom_order_filter_list'] );
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks()
    {
        $plugin_public = new Incert_Coupons_Public($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');

        remove_filter('woocommerce_coupon_is_valid', 'woocommerce_coupon_is_valid', 10);
        add_filter('woocommerce_get_shop_coupon_data', [$this, 'custom_woocommerce_get_shop_coupon_data'], 1, 2);
        add_filter('woocommerce_coupon_is_valid', [$this, 'custom_woocommerce_coupon_is_valid'], 999, 2);
        add_filter('woocommerce_coupon_error', [$this, 'custom_woocommerce_coupon_error'], 10, 3);
        add_action('woocommerce_cart_calculate_fees', [$this, 'custom_woocommerce_cart_calculate_fees'], 10, 1);
        add_action('woocommerce_thankyou', [$this, 'custom_order_success'], 10, 1); // woocommerce_order_status_completed
        add_action('woocommerce_order_status_failed', [$this, 'custom_order_revert'], 10, 1);
        add_action('woocommerce_order_status_refunded', [$this, 'custom_order_revert'], 10, 1);
        add_action('woocommerce_order_status_cancelled', [$this, 'custom_order_revert'], 10, 1);
        add_action('woocommerce_order_status_completed', [$this, 'custom_order_incert_articles'], 10, 1); // woocommerce_payment_complete, woocommerce_thankyou
        add_action('woocommerce_store_api_checkout_order_processed', [$this, 'custom_check_order'], 10, 1);
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run()
    {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @return    string    The name of the plugin.
     * @since     1.0.0
     */
    public function get_plugin_name()
    {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @return    Incert_Coupons_Loader    Orchestrates the hooks of the plugin.
     * @since     1.0.0
     */
    public function get_loader()
    {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @return    string    The version number of the plugin.
     * @since     1.0.0
     */
    public function get_version()
    {
        return $this->version;
    }

    /**
     * Menu
     */
    public function custom_admin_menu()
    {
        add_submenu_page(
            'woocommerce-marketing',
            esc_html__('Incert Plugin', 'incert-coupons'),
            esc_html__('Incert Plugin', 'incert-coupons'),
            'edit_posts',
            'incert_coupons',
            [$this, 'incert_coupons_page'],
            10
        );

        add_submenu_page(
            'woocommerce-marketing',
            esc_html__('Incert Bestellungen exportieren', 'incert-coupons'),
            esc_html__('Incert Bestellungen exportieren', 'incert-coupons'),
            'edit_posts',
            'incert_coupons_export',
            [$this, 'incert_coupons_export_page'],
            11
        );

        add_action( 'admin_init', function() {
            if ( isset( $_POST['incert_coupons_export_page'] ) ) {
                if ( isset( $_POST['_wpnonce'] ) && !wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['_wpnonce'] ) ), 'incert_coupons_export_page' ) ) {
                    wp_die( esc_html__( 'Ungültiger Nonce.', 'incert-coupons' ) );
                }

                if ( ! current_user_can( 'edit_posts' ) ) {
                    wp_die( esc_html__( 'Keine Berechtigung.', 'incert-coupons' ) );
                }

                header( 'Content-Type: text/csv; charset=UTF-8' );
                header( 'Content-Disposition: attachment; filename=incert_export_' . gmdate( 'Y-m-d_H-i-s' ) . '.csv' );
                header( 'Pragma: no-cache' );
                header( 'Expires: 0' );

                echo esc_html__('Bestell-ID', 'incert-coupons') . ';';
                //echo esc_html__('Bestell-Link', 'incert-coupons') . ';';
                echo esc_html__('Datum', 'incert-coupons') . ';';
                echo esc_html__('Summe', 'incert-coupons') . ';';
                echo esc_html__('Incert Gutschein eingelöst', 'incert-coupons') . ';';
                echo esc_html__('Incert Gutschein gekauft', 'incert-coupons') . "\n";
                echo "\n";

                $orders = wc_get_orders( [ 'limit' => -1 ] );

                foreach ( $orders as $order ) {
                    $incertCouponUsed = get_post_meta($order->get_id(), '_incert_coupon_used', true) ? esc_html__('Ja', 'incert-coupons') : esc_html__('Nein', 'incert-coupons');
                    $hasIncertProduct = get_post_meta($order->get_id(), '_incert_has_incert_product', true) ? esc_html__('Ja', 'incert-coupons') : esc_html__('Nein', 'incert-coupons');

                    echo esc_html($order->get_id() . ';');
                    //echo admin_url( 'post.php?post=' . $order->get_id() . '&action=edit' ) . ';';
                    echo esc_html(( $order->get_date_created() ? $order->get_date_created()->date_i18n( 'Y-m-d H:i:s' ) : '' ) . ';');
                    echo esc_html($order->get_total() . ';');
                    echo esc_html($incertCouponUsed . ';');
                    echo esc_html($hasIncertProduct . "\n");
                }

                exit;
            }
        });
    }

    /**
     * Options page
     */
    public function incert_coupons_page()
    {
        ?>

        <div class="wrap">
            <h1>
                <?php echo esc_html__('Incert Plugin', 'incert-coupons'); ?>
            </h1>

            <?php

            /* save data */
            if (isset($_POST['incert_coupons_page'])) {
                if ( isset( $_POST['_wpnonce'] ) && !wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['_wpnonce'] ) ), 'incert_coupons_page' ) ) {
                    echo '<div class="error"><p>' . esc_html__('Fehler', 'incert-coupons') . '</p></div>';
                } else {
                    $this->incert_coupons_page_save();

                    if (!$this->access_token_valid()) {
                        echo '<div class="error"><p>' . esc_html__('Fehler: Keine Verbindung zu Schnittstelle möglich', 'incert-coupons') . '</p></div>';
                    } else {
                        echo '<div class="notice notice-success"><p>' . esc_html__('Daten gespeichert; Verbindung zu Schnittstelle erfolgreich getestet.', 'incert-coupons') . '</p></div>';
                    }
                }
            }

            /* get data */
            $options = get_option('incert_coupons');

            if (!$options) {
                add_option('incert_coupons', '');
            } else {
                $options = unserialize($options);
            }

            ?>

            <form action="<?php echo esc_html( sanitize_text_field( wp_unslash( $_SERVER['REQUEST_URI'] ?? '' ) ) ); ?>" method="post" autocomplete="off">
                <?php wp_nonce_field('incert_coupons_page'); ?>
                <input type="hidden" name="incert_coupons_page" value="1">

                <table class="form-table" role="presentation">
                    <tbody>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Aktiviert', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <select name="incert_coupons[active]">
                                <option value="0" <?php if (empty($options['active'])) { ?>selected<?php } ?>>
                                    <?php echo esc_html__('Nein', 'incert-coupons'); ?>
                                </option>
                                <option value="1" <?php if (!empty($options['active'])) { ?>selected<?php } ?>>
                                    <?php echo esc_html__('Ja', 'incert-coupons'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('API-URL', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="incert_coupons[api_url]" size="50" value="<?php echo esc_html($options['api_url'] ?? '') ?>">
                            <p class="description">
                                <?php echo esc_html__('z.B. https://api-sandbox.myincert.com/api, https://api.myincert.com/api', 'incert-coupons'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('API-Schlüssel', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="incert_coupons[api_key]" size="50" value="<?php echo esc_html($options['api_key'] ?? '') ?>">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Client ID', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="incert_coupons[client_id]" size="50" value="<?php echo esc_html($options['client_id'] ?? '') ?>">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Kundengeheimnis', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="incert_coupons[client_secret]" size="50" value="<?php echo esc_html($options['client_secret'] ?? '') ?>">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Buchungspartner ID', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <input type="text" name="incert_coupons[booking_partner_id]" size="50" value="<?php echo esc_html($options['booking_partner_id'] ?? '') ?>">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Produkt SKUs', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <textarea name="incert_coupons[product_skus]" cols="50" rows="10"><?php echo esc_html($options['product_skus'] ?? '') ?></textarea>
                            <p class="description">
                                <?php echo esc_html__('Folgende Produkte für die Anwendung von incert Gutscheinen ausnehmen; eine Produkt-SKU pro Zeile', 'incert-coupons'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php echo esc_html__('Kostenlose Lieferung bei Gutscheinen aktivieren', 'incert-coupons'); ?></label>
                        </th>
                        <td>
                            <select name="incert_coupons[free_shipping]">
                                <option value="0" <?php if (empty($options['free_shipping'])) { ?>selected<?php } ?>>
                                    <?php echo esc_html__('Nein', 'incert-coupons'); ?>
                                </option>
                                <option value="1" <?php if (!empty($options['free_shipping'])) { ?>selected<?php } ?>>
                                    <?php echo esc_html__('Ja', 'incert-coupons'); ?>
                                </option>
                            </select>
                            <p class="description">
                                <?php echo esc_html__('Wenn ausgewählt wird die Option "Kostenlose Lieferung" für Incert Gutscheine aktiviert.', 'incert-coupons'); ?>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <p class="submit">
                    <input class="button-primary" type="submit" value="<?php echo esc_html__('Speichern', 'incert-coupons'); ?>">
                </p>
            </form>
        </div>

        <?php
    }

    public function incert_coupons_page_save()
    {
        if ( isset( $_POST['_wpnonce'] ) && !wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['_wpnonce'] ) ), 'incert_coupons_page' ) ) {
            wp_die( esc_html__( 'Ungültiger Nonce', 'incert-coupons' ) );
        }

        if( isset( $_POST['incert_coupons'] ) ) {
            $dataArray = [
                'active'  => isset( $_POST['incert_coupons']['active'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['active'] ) ) : '',
                'api_url' => isset( $_POST['incert_coupons']['api_url'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['api_url'] ) ) : '',
                'api_key' => isset( $_POST['incert_coupons']['api_key'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['api_key'] ) ) : '',
                'client_id' => isset( $_POST['incert_coupons']['client_id'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['client_id'] ) ) : '',
                'client_secret' => isset( $_POST['incert_coupons']['client_secret'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['client_secret'] ) ) : '',
                'booking_partner_id' => isset( $_POST['incert_coupons']['booking_partner_id'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['booking_partner_id'] ) ) : '',
                'product_skus' => isset( $_POST['incert_coupons']['product_skus'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['product_skus'] ) ) : '',
                'free_shipping' => isset( $_POST['incert_coupons']['free_shipping'] ) ? sanitize_text_field( wp_unslash( $_POST['incert_coupons']['free_shipping'] ) ) : '',
            ];

            update_option( 'incert_coupons', serialize( $dataArray ) );
        }
    }

    /**
     * Options page
     */
    public function incert_coupons_export_page()
    {
        ?>

        <div class="wrap">
            <h1>
                <?php echo esc_html__('Incert Bestellungen exportieren', 'incert-coupons'); ?>
            </h1>

            <form action="<?php echo esc_html( sanitize_text_field( wp_unslash( $_SERVER['REQUEST_URI'] ?? '' ) ) ); ?>" method="post" autocomplete="off">
                <?php wp_nonce_field('incert_coupons_export_page'); ?>
                <input type="hidden" name="incert_coupons_export_page" value="1">

                <p class="submit">
                    <input class="button-primary" type="submit" value="<?php echo esc_html__('Exportieren', 'incert-coupons'); ?>">
                </p>
            </form>
        </div>

        <?php
    }

    /**
     * Coupon listing
     */
    public function custom_add_coupon_column($columns)
    {
        $new_columns = array();
        foreach ($columns as $key => $title) {
            $new_columns[$key] = $title;

            if ($key == 'type') {
                $new_columns['free_shipping'] = esc_html__('Kostenloser Versand', 'incert-coupons');
            }

            if ($key == 'type') {
                $new_columns['is_incert'] = esc_html__('Incert Gutschein', 'incert-coupons');
            }

            if ($key == 'type') {
                $new_columns['partly_redeemable'] = esc_html__('Teileinlösbar', 'incert-coupons');
            }

            if ($key == 'products') {
                $new_columns['products_excluded'] = esc_html__('Ausgeschlossene Produkte', 'incert-coupons');
            }
        }
        return $new_columns;
    }

    public function custom_coupon_column_data($column, $postid)
    {
        if ('free_shipping' === $column) {
            $coupon = new WC_Coupon($postid);
            $freeShipping = $coupon->get_free_shipping();
            echo $freeShipping ? esc_html__('Ja', 'incert-coupons') : esc_html__('Nein', 'incert-coupons');
        }

        if ('is_incert' === $column) {
            $isIncert = intval(get_post_meta($postid, '_is_incert', true));
            echo $isIncert ? esc_html__('Ja', 'incert-coupons') : esc_html__('Nein', 'incert-coupons');
        }

        if ('partly_redeemable' === $column) {
            $isIncert = intval(get_post_meta($postid, '_is_incert', true));
            if ($isIncert) {
                $partlyRedeemable = intval(get_post_meta($postid, '_partly_redeemable', true));
                echo $partlyRedeemable ? esc_html__('Ja', 'incert-coupons') : esc_html__('Nein', 'incert-coupons');
            }
            else {
                echo esc_html__('Ja', 'incert-coupons');
            }
        }

        if ('products_excluded' === $column) {
            $coupon = new WC_Coupon($postid);
            $excluded_product_ids = $coupon->get_excluded_product_ids();

            if (!empty($excluded_product_ids)) {
                $product_names = array();

                foreach ($excluded_product_ids as $product_id) {
                    $product = wc_get_product($product_id);
                    if ($product) {
                        $product_names[] = $product->get_name();
                    }
                }

                echo esc_html(implode(', ', $product_names));
            } else {
                echo esc_html__('-', 'incert-coupons');
            }
        }
    }

    public function custom_coupon_editing_restrictions()
    {
        $post = isset( $_GET[ 'post' ] ) ? get_post( sanitize_text_field( wp_unslash( $_GET['post'] ) ) ) : false;
        $action = isset( $_GET[ 'action' ] ) ? sanitize_text_field( wp_unslash( $_GET['action'] ) ) : '';

        if ($post && $post->post_type === 'shop_coupon' && is_object($post)) {
            $isIncert = intval(get_post_meta($post->ID, '_is_incert', true));

            if ($isIncert && ( !isset($_GET['action']) || $action !== 'trash' ) ) {
                wp_die(esc_html__('Dieser Gutschein kann nicht bearbeitet werden.', 'incert-coupons'));
            }
        }
    }

    public function is_active()
    {
        $options = $this->get_options();

        if ($options) {
            if (!empty($options['active'])) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function get_options()
    {
        $options = get_option('incert_coupons');

        if ($options) {
            return unserialize($options);
        } else {
            return null;
        }
    }

    public function options_valid()
    {
        $options = $this->get_options();

        if ($options) {
            if (!empty($options['api_url']) && !empty($options['api_key']) && !empty($options['client_id']) && !empty($options['client_secret'])) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function access_token_valid()
    {
        $apiInstance = $this->get_api_instance();

        if ($apiInstance) {
            $accessToken = $apiInstance->getConfig()->getAccessToken();

            if ($accessToken) {
                return true;
            }
        }

        return false;
    }

    public function get_api_instance($shopApi = false, $countryApi = false)
    {
        if ($this->options_valid()) {
            $options = $this->get_options();
            $config = new Incert\Client\Configuration();
            $config->setApiKey('X-Api-Key', $options['api_key']);
            $config->setHost($options['api_url']);
            $apiInstance = new Incert\Client\Api\OAuth20ClientCredentialsApi($config);
            $accessToken = new \Incert\Client\OAuth20\GrantAccessTokenRequest();

            try {
                $accessToken->setClientId($options['client_id'])->setClientSecret($options['client_secret']);
                $tokenResult = $apiInstance->oauthAccessTokenPost($accessToken);
                $config->setAccessToken($tokenResult->getAccessToken());
                if ($shopApi) {
                    $apiInstance = new Incert\Client\Api\ShopApi($config);
                }
                elseif($countryApi) {
                    $apiInstance = new Incert\Client\Api\CountryApi($config);
                }
                else {
                    $apiInstance = new Incert\Client\Api\RedemptionApi($config);
                }

                return $apiInstance;
            } catch (Exception $e) {
                return false;
            }
        } else {
            return false;
        }
    }

    public function get_product_ids()
    {
        $productIds = [];
        $options = $this->get_options();

        if ($options && !empty($options['product_skus'])) {
            $input = str_replace(array("\r\n", "\r"), "\n", $options['product_skus']);
            $productSkus = explode("\n", $input);
            $productSkus = array_filter($productSkus, 'strlen');


            foreach ($productSkus as $productSku) {
                $productId = wc_get_product_id_by_sku($productSku);

                if ($productId) {
                    $productIds[] = $productId;
                }
            }
        }

        return $productIds;
    }

    /**
     * Validate coupon on usage
     * @param $false
     * @param $coupon_code
     * @return array|mixed
     */
    public function custom_woocommerce_get_shop_coupon_data($false, $coupon_code)
    {
        if (is_admin()) {
            return false;
        }

        $isIncertCoupon = false;
        $apiInstance = $this->get_api_instance();
        $options = $this->get_options();

        if ($apiInstance && $this->is_active()) {
            $result = $apiInstance->shopV2RedeemStatusCodeGet($coupon_code, $options['booking_partner_id'] ?? null);
            $resultData = $result->getData();

            if (isset($resultData['status']) && $resultData['status'] == 1) {
                $result = $apiInstance->shopV2RedeemStatusDetailCodeGet($coupon_code, $options['booking_partner_id'] ?? null);
                $resultData = $result->getData();
                $isIncertCoupon = true;
            }
        }

        if ($isIncertCoupon) {
            $coupon_id = wc_get_coupon_id_by_code($coupon_code);
            $discount_type = 'fixed_cart';
            $amount = $resultData['currentAmount'] ?? null;
            $description = $resultData['articleDescription'] ?? null;
            $excludedProductIds = $this->get_product_ids();
            $partlyRedeemable = $resultData['partlyRedeemable'] ?? false;
            if(!empty($options['free_shipping']) && $options['free_shipping'] == '1') {
                $free_shipping = true;
            }
            else {
                $free_shipping = false;
            }

            /* update coupon data */
            if ($coupon_id) {
                $props = [
                    'code' => $coupon_code,
                    'discount_type' => $discount_type,
                    'amount' => $amount,
                    'individual_use' => false,
                    'product_ids' => [],
                    'excluded_product_ids' => $excludedProductIds,
                    'usage_limit' => '',
                    'usage_limit_per_user' => '',
                    'limit_usage_to_x_items' => null,
                    'free_shipping' => $free_shipping,
                    'product_categories' => [],
                    'excluded_product_categories' => [],
                    'exclude_sale_items' => false,
                    'minimum_amount' => '',
                    'maximum_amount' => '',
                    'email_restrictions' => [],
                    'virtual' => 'yes',
                    'description' => $description,
                ];

                $this->custom_update_coupon($coupon_id, $props, $partlyRedeemable, true);

                return false;

                /* create coupon with data */
            } else {
                $props = [
                    'code' => $coupon_code,
                    'discount_type' => $discount_type,
                    'amount' => $amount,
                    'individual_use' => false,
                    'product_ids' => [],
                    'excluded_product_ids' => $excludedProductIds,
                    'usage_limit' => '',
                    'usage_limit_per_user' => '',
                    'limit_usage_to_x_items' => null,
                    'free_shipping' => $free_shipping,
                    'product_categories' => [],
                    'excluded_product_categories' => [],
                    'exclude_sale_items' => false,
                    'minimum_amount' => '',
                    'maximum_amount' => '',
                    'email_restrictions' => [],
                    'virtual' => 'yes',
                    'description' => $description,
                ];

                $coupon = new WC_Coupon();
                $this->custom_update_coupon($coupon->get_id(), $props, $partlyRedeemable, true);

                return $coupon;
            }
        }

        return $false;
    }

    public function custom_woocommerce_coupon_is_valid( $is_valid, $coupon ) {
        $couponAmount = floatval($coupon->get_amount());
        $cart = WC()->cart;
        $cartSubTotal = floatval($cart->get_subtotal());
        $cartDiscount = 0.0;
        foreach ($cart->get_applied_coupons() as $applied_coupon_code) {
            if ($applied_coupon_code === $coupon->get_code()) {
                continue;
            }

            $applied_coupon = new WC_Coupon($applied_coupon_code);
            $cartDiscount += floatval($applied_coupon->get_amount());
        }
        $cartTotal = $cartSubTotal - $cartDiscount;

        $isIncert = intval(get_post_meta($coupon->get_id(), '_is_incert', true));
        $partlyRedeemable = intval(get_post_meta($coupon->get_id(), '_partly_redeemable', true));

        if ($cartTotal && $isIncert && !$partlyRedeemable && $couponAmount > $cartTotal) {
            $is_valid = false;
        }

        return $is_valid;
    }

    public function custom_woocommerce_coupon_error( $err, $err_code, $coupon ) {
        if (is_admin()) {
            return false;
        }

        if ( $err_code === WC_Coupon::E_WC_COUPON_NOT_EXIST ) {
            $apiInstance = $this->get_api_instance();
            $options = $this->get_options();

            if ($apiInstance && $this->is_active()) {
                $result = $apiInstance->shopV2RedeemStatusCodeGet($coupon->get_code(), $options['booking_partner_id'] ?? null);
                $resultData = $result->getData();

                switch ($resultData['status']) {
                    case 0:
                        return esc_html__( 'Gutschein nicht gefunden', 'incert-coupons' );
                    case 2:
                        return esc_html__( 'Gutschein storniert', 'incert-coupons' );
                    case 3:
                        return esc_html__( 'Gutschein vollständig eingelöst', 'incert-coupons' );
                    case 4:
                        return esc_html__( 'Gutscheintyp nicht erlaubt', 'incert-coupons' );
                    case 5:
                        return esc_html__( 'Gutschein noch nicht eingelöst', 'incert-coupons' );
                    case 6:
                        return esc_html__( 'Gutschein ist bereits abgelaufen', 'incert-coupons' );
                    case 7:
                        return esc_html__( 'Gutschein ist für diese Station nicht gültig', 'incert-coupons' );
                    case 8:
                        return esc_html__( 'Gutschein nicht aktiviert', 'incert-coupons' );
                    case 9:
                        return esc_html__( 'Gutschein ist für diesen Tarifcode nicht gültig', 'incert-coupons' );
                    case 10:
                        return esc_html__( 'Gutschein erfordert Pin', 'incert-coupons' );
                    case 11:
                        return esc_html__( 'Gutschein nicht in Ihrer Währung verfügbar', 'incert-coupons' );
                }
            }
        }

        return $err;
    }

    public function custom_woocommerce_cart_calculate_fees() {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        $cart = WC()->cart;

        /* get total discount for all coupons */
        $couponsDiscountTotal = 0;
        $appliedCoupons = WC()->cart->get_applied_coupons();
        if (!empty($appliedCoupons)) {
            foreach ($appliedCoupons as $coupon_code) {
                $coupon = new WC_Coupon($coupon_code);
                $couponsDiscountTotal += floatval($coupon->get_amount());
            }
        }

        /* get cart subtotal */
        $cartTotal = floatval($cart->get_subtotal()) + floatval($cart->get_subtotal_tax());

        /* get shipping costs */
        $shippingTotal = floatval($cart->get_shipping_total()) + floatval($cart->get_shipping_tax());

        /* calculate discount left */
        $couponsDiscountLeft = $couponsDiscountTotal - $cartTotal;

        if ($couponsDiscountLeft < 0 || $shippingTotal == 0) {
            return;
        }

        /* calculate shipping discount */
        $shippingDiscount = 0;
        if ($couponsDiscountLeft > $shippingTotal) {
            $shippingDiscount = $shippingTotal;
        }
        else {
            $shippingDiscount = $couponsDiscountLeft;

            /* remove tax from shipping discount */
            $cartSubtotal = floatval($cart->get_subtotal());
            $cartTax = floatval($cart->get_subtotal_tax());
            $cartTotal = $cartSubtotal + $cartTax;
            $taxRate = ($cartTax / $cartSubtotal);
            $taxFactor = 1 + $taxRate;
            $shippingDiscount = $shippingDiscount / $taxFactor;
        }

        /* apply shipping discount */
        WC()->cart->add_fee(esc_html__('Rabatt Versandkosten', 'incert-coupons'), -$shippingDiscount);
    }

    public function custom_update_coupon($coupon_id, $props, $partlyRedeemable = null, $partlyRedeemableSet = false) {
        $coupon = new WC_Coupon($coupon_id);
        $coupon->set_props($props);
        $coupon->save();
        update_post_meta($coupon_id, '_is_incert', '1');
        if ($partlyRedeemableSet) {
            if ($partlyRedeemable) {
                update_post_meta($coupon_id, '_partly_redeemable', 1);
            }
            else {
                update_post_meta($coupon_id, '_partly_redeemable', 0);
            }
        }
    }

    /**
     * Set coupon on "used" after successful order
     * @param $order_id
     * @return void
     */
    public function custom_order_success($order_id)
    {
        $order = wc_get_order($order_id);
        $couponCodes = $order->get_coupon_codes();
        $apiInstance = $this->get_api_instance();
        $amountSum = 0;
        $incertCouponUsed = false;

        if ($apiInstance && $this->is_active()) {
            if ($couponCodes) {
                foreach ($couponCodes as $couponCode) {
                    $coupon_id = wc_get_coupon_id_by_code($couponCode);
                    $coupon = new WC_Coupon($coupon_id);
                    $isIncert = intval(get_post_meta($coupon_id, '_is_incert', true));

                    if ($isIncert) {
                        $incertCouponUsed = true;

                        /* get actual amount of coupon to redeem */
                        $amount = $this->custom_get_coupon_order_amount($order_id, $coupon_id, $amountSum);

                        /* redeem via api */
                        $redeemRequest = new \Incert\Client\Model\Redeem\Request();
                        $redeemRequest->setCode($couponCode);
                        $redeemRequest->setAmount($amount);
                        $redeemRequest->setCurrency(get_woocommerce_currency());
                        $result = $apiInstance->shopV2RedeemPost($redeemRequest);
                        $newAmount = $coupon->get_amount();

                        try {
                            if (method_exists($result, 'getRedemptionID') && method_exists($result, 'getRedeemedAmount')) {
                                $redemptionId = $result->getRedemptionID();
                                $redeemedAmount = $result->getRedeemedAmount();
                                $newAmount = $coupon->get_amount() - $redeemedAmount;
                                update_post_meta($coupon_id, '_order_' . $order_id . '_redemption_id', $redemptionId);
                                update_post_meta($coupon_id, '_order_' . $order_id . '_redeemed_amount', $redeemedAmount);
                            }
                        } catch (Exception $e) {
                        }

                        /* update amount */
                        $this->custom_update_coupon($coupon_id, ['amount' => $newAmount]);
                    }
                    else {
                        $amount = floatval($coupon->get_amount());
                    }

                    $amountSum += $amount;
                }
            }
        }

        if ($incertCouponUsed) {
            update_post_meta($order_id, '_incert_coupon_used', '1');
            $order = wc_get_order($order_id);
            $order->update_meta_data( '_incert_coupon_used', '1' );
            $order->save();
        }
    }

    public function custom_get_coupon_order_amount($order_id, $coupon_id, $amountSum) {
        /* get order and coupon */
        $order = wc_get_order($order_id);
        $coupon = new WC_Coupon($coupon_id);

        /* get order subtotal */
        $orderTotal = floatval($order->get_subtotal());

        /* get shipping costs */
        $shippingTotal = floatval($order->get_shipping_total());
        $orderTotalWithShipping = $orderTotal + $shippingTotal;
        $orderTotalLeft = $orderTotalWithShipping - $amountSum;

        /* calculate amount */
        if($orderTotalLeft > $coupon->get_amount()) {
            return $coupon->get_amount();
        }
        else {
            return $orderTotalLeft;
        }
    }

    /**
     * Take back redemption when order is reverted
     * @param $order_id
     * @return void
     */
    public function custom_order_revert($order_id)
    {
        $order = wc_get_order($order_id);
        $couponCodes = $order->get_coupon_codes();
        $apiInstance = $this->get_api_instance();

        if ($apiInstance && $this->is_active()) {
            /* cancel coupons */
            if ($couponCodes) {
                foreach ($couponCodes as $couponCode) {
                    $coupon_id = wc_get_coupon_id_by_code($couponCode);
                    $coupon = new WC_Coupon($coupon_id);
                    $isIncert = intval(get_post_meta($coupon_id, '_is_incert', true));

                    if ($isIncert) {
                        /* cancel redemption */
                        $redemptionId = get_post_meta($coupon_id, '_order_' . $order_id . '_redemption_id', true);
                        $redeemedAmount = get_post_meta($coupon_id, '_order_' . $order_id . '_redeemed_amount', true);
                        /* do not cancel redeem, just recharge */
                        //$cancelRedemption = new \Incert\Client\Model\Redemption\CancelRequest();
                        //$cancelRedemption->setRedemptionID($redemptionId);
                        //$result = $apiInstance->shopV2RedeemCancelPost($cancelRedemption);

                        /* recharge */
                        $rechargeVoucher = new \Incert\Client\Model\Redemption\RechargeVoucher();
                        $rechargeVoucher->setCode($couponCode);
                        $rechargeVoucher->setAmount($redeemedAmount);
                        $rechargeVoucher->setCurrency(get_woocommerce_currency());
                        $result = $apiInstance->shopV2RedeemRechargeVoucherPost($rechargeVoucher);

                        try {
                            $resultData = $result->getData();

                            if (isset($resultData['currentAmount'])) {
                                $newAmount = $resultData['currentAmount'];
                                $this->custom_update_coupon($coupon_id, ['amount' => $newAmount]);
                            }

                        } catch (Exception $e) {
                        }
                    }
                }
            }
        }

        $apiInstance = $this->get_api_instance(true);

        if ($apiInstance && $this->is_active()) {
            /* cancel incert coupon articles */
            $incertOrderId = get_post_meta($order_id, '_incert_order_id', true);
            if ($incertOrderId) {
                $result = $apiInstance->shopV2OrderCancelPost($incertOrderId);
            }
        }
    }

    public function custom_product_add_incert_coupon_dropdown()
    {
        global $post;

        $values = get_post_meta($post->ID, '_incert_coupon_articles', true);
        $products = $this->get_incert_coupon_articles();
        $options = [];

        foreach($products as $product) {
            $productDescriptions = $product->getProductDescriptions();
            $name = $productDescriptions[0]['name'] ?? '';
            $name .= ' (ID:' . $product->getId() .')';
            $options[$product->getId()] = $name;
        }

        echo '<div class="options_group">';
        woocommerce_wp_select(array(
            'id' => 'incert_coupon_articles',
            'name' => 'incert_coupon_articles[]',
            'label' => esc_html__('Incert Gutscheine', 'incert-coupons'),
            'desc_tip' => true,
            'description' => esc_html__('Wählen Sie einen oder mehrere Incert Gutscheine, für welche dieses Produkt gültig ist.', 'incert-coupons'),
            'value' => $values,
            'options' => $options,
            'custom_attributes' => array('size' => '10'),
        ));
        echo '</div>';
    }

    public function custom_product_save_incert_coupon_dropdown($post_id)
    {
        if ( isset( $_POST['woocommerce_meta_nonce'] ) && !wp_verify_nonce( sanitize_text_field( wp_unslash( $_POST['woocommerce_meta_nonce'] ) ), 'woocommerce_save_data' ) ) {
            wp_die( esc_html__( 'Ungültiger Nonce.', 'incert-coupons' ) );
        }

        $custom_multiselect_field = isset($_POST['incert_coupon_articles']) ? array_map('sanitize_text_field', wp_unslash( $_POST['incert_coupon_articles'] ) ) : array();
        update_post_meta($post_id, '_incert_coupon_articles', $custom_multiselect_field);
    }

    public function get_incert_coupon_articles() {
        $apiInstance = $this->get_api_instance(true);
        $products = [];

        if ($apiInstance && $this->is_active()) {
            $result = $apiInstance->shopV2CoreProductGet(get_option('woocommerce_default_country'), get_woocommerce_currency());

            if( $result instanceof \Incert\Client\Model\Shop\Response\Product\Collection ) {
                try {
                    $products = $result->getProducts();
                } catch (Exception $e) {
                }
            }
        }

        return $products;
    }

    public function custom_order_incert_articles($order_id) {
        $order = wc_get_order($order_id);
        $order_items = $order->get_items();
        $apiInstance = $this->get_api_instance(true);

        if ($apiInstance && $this->is_active()) {
            $result = $apiInstance->shopV2ModulesCoreGet('de');
            $resultData = $result->getData();
            $shippingResult = $resultData['shipping'][0] ?? null;
            $shippingResultData = $shippingResult->getData() ?? null;
            $shippingModule = $shippingResultData['module'] ?? '';
            $paymentResult = $resultData['payment'][0] ?? null;
            $paymentResultData = $paymentResult->getData() ?? null;
            $paymentModule = $paymentResultData['module'] ?? '';
            $shoppingCart['products'] = array();

            foreach ($order_items as $order_item) {
                $incertCouponArticleIds = get_post_meta($order_item->get_product_id(), '_incert_coupon_articles', true);
                $incertCouponArticleId = $incertCouponArticleIds[0] ?? null;

                if ($incertCouponArticleId) {
                    /* build order request */
                    $shoppingCart['products'][] = array(
                        'id' => $incertCouponArticleId,
                        'quantity' => $order_item->get_quantity(),
                        'voucherValue' => $order_item->get_product()->get_price(),
                    );
                }
            }

            if( count( $shoppingCart['products'] ) ) {
                $customer = array(
                    'email' => $order->get_billing_email(),
                    'firstName' => $order->get_billing_first_name(),
                    'lastName' => $order->get_billing_last_name(),
                    'paymentAddress' => [
                        'firstName' => $order->get_billing_first_name(),
                        'lastName' => $order->get_billing_last_name(),
                        'city' => $order->get_billing_city(),
                        'postalCode' => $order->get_billing_postcode(),
                        'street' => $order->get_billing_address_1(),
                        'country' => $order->get_billing_country(),
                    ],
                );

                $orderRequest = new \Incert\Client\Model\Shop\Request\Order();
                $orderRequest->setShoppingCart($shoppingCart);
                $orderRequest->setShippingModule($shippingModule);
                $orderRequest->setPaymentModule($paymentModule);
                $orderRequest->setCustomer($customer);
                $orderRequest->setSendInvoice(true);

                /* submit order */
                $incertOrderId = null;

                try {
                    $result = $apiInstance->shopV2OrderPost($order->get_billing_country(), get_woocommerce_currency(), $orderRequest);
                    $incertOrderId = $result->getOrderId() ?? null;
                } catch (Exception $e) {
                }

                if ($incertOrderId) {
                    update_post_meta($order_id, '_incert_order_id', $incertOrderId);
                    update_post_meta($order_id, '_incert_order_error', false);
                }
                else {
                    update_post_meta($order_id, '_incert_order_id', $incertOrderId);
                    update_post_meta($order_id, '_incert_order_error', true);
                }

                update_post_meta($order_id, '_incert_has_incert_product', '1');
                $order = wc_get_order($order_id);
                $order->update_meta_data( '_incert_has_incert_product', '1' );
                $order->save();
            }
        }
    }

    public function custom_order_details_data( $order ) {
        $incert_order_id = get_post_meta( $order->get_id(), '_incert_order_id', true );
        $incert_order_error = get_post_meta( $order->get_id(), '_incert_order_error', true );

        echo '<div class="order_data_column">';
        echo '<h4>' . esc_html__( 'Incert Gutscheine Bestelldetails', 'incert-coupons' ) . '</h4>';
        echo '<p><strong>' . esc_html__( 'Incert Order ID', 'incert-coupons' ) . ':</strong> ' . esc_html($incert_order_id) . '</p>';
        echo '<p><strong>' . esc_html__( 'Incert Order Error', 'incert-coupons' ) . ':</strong> ' . esc_html($incert_order_error) . '</p>';
        echo '</div>';
    }

    public function custom_product_save_action($product_id) {
        $incert_coupon_articles_data_set = get_post_meta($product_id, '_incert_coupon_articles_data_set', true);
        if(!empty($incert_coupon_articles_data_set)) return;

        $product = wc_get_product($product_id);
        $incertProducts = $this->get_incert_coupon_articles();
        $incertCouponArticleIds = get_post_meta($product_id, '_incert_coupon_articles', true);
        $incertCouponArticleId = $incertCouponArticleIds[0] ?? null;

        /* set incert product data initially */
        foreach( $incertProducts as $incertProduct ) {
            if($incertProduct->getId() == $incertCouponArticleId) {
                update_post_meta($product_id, '_incert_coupon_articles_data_set', '1');

                $productDescriptions = $incertProduct->getProductDescriptions();
                $name = $productDescriptions[0]['name'] ?? '';
                $description = $productDescriptions[0]['description'] ?? '';
                $shortDescription = $productDescriptions[0]['shortDescription'] ?? '';
                $keywords = $productDescriptions[0]['keywords'] ?? '';

                $product->set_name($name);
                $product->set_description($description);
                $product->set_short_description($shortDescription);
                $product->set_virtual(true);
                $product->save();

                wp_set_object_terms($product_id, [$keywords], 'product_tag');
                $imageUrl = $incertProduct->getImageLink();

                if( $imageUrl ) {
                    $this->upload_set_post_thumbnail( $product_id, $imageUrl );
                }
            }
        }
    }

    function upload_set_post_thumbnail($product_id, $image_url)
    {
        global $wp_filesystem;

        if (!function_exists('WP_Filesystem')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        WP_Filesystem();

        $image_name = basename($image_url);
        $response = wp_remote_get($image_url);

        if (
            !is_wp_error($response)
            && wp_remote_retrieve_response_code($response) === 200
        ) {
            $image_data = wp_remote_retrieve_body($response);
            $upload_dir = wp_upload_dir();
            $image_path = $upload_dir['path'] . '/' . $image_name;
            $wp_filesystem->put_contents($image_path, $image_data, FS_CHMOD_FILE);
            $wp_filetype = wp_check_filetype($image_name, null);

            $attachment = array(
                'post_mime_type' => $wp_filetype['type'],
                'post_title' => sanitize_file_name($image_name),
                'post_content' => '',
                'post_status' => 'inherit',
            );

            $attachment_id = wp_insert_attachment($attachment, $image_path);

            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $image_path);
            wp_update_attachment_metadata($attachment_id, $attachment_data);

            set_post_thumbnail($product_id, $attachment_id);
        }
    }


    public function custom_check_order($order) {
        if (!is_a($order, 'WC_Order')) {
            return;
        }

        try {
            $order_items = $order->get_items();
            $billing_country = $order->get_billing_country();
            $apiInstance = $this->get_api_instance(false, true);

            if ($apiInstance && $this->is_active()) {
                foreach ($order_items as $order_item) {
                    $incertCouponArticleIds = get_post_meta($order_item->get_product_id(), '_incert_coupon_articles', true);
                    $incertCouponArticleId = $incertCouponArticleIds[0] ?? null;

                    if ($incertCouponArticleId) {
                        $countryRequest = new \Incert\Client\Model\Country\ExistsRequest();
                        $countryRequest->setIsoCode2($billing_country);
                        $result = $apiInstance->shopV2CountryExistsPost($countryRequest);
                        $resultData = $result->getData();

                        if(empty($resultData['status'])) {
                            wc_add_notice(esc_html__('Dieser Incert Gutschein kann nicht aus Ihrem Land bestellt werden.', 'incert-coupons'), 'error');
                            $order->delete(true);
                            return;
                        }
                    }
                }
            }
        } catch (Exception $e) {
        }
    }

    public function custom_orders_new_column($columns) {
        $columns['coupons_used'] = esc_html__('Gutscheine', 'incert-coupons');
        $columns['incert_coupon_used'] = esc_html__('Incert Gutschein eingelöst', 'incert-coupons');
        $columns['is_incert_order'] = esc_html__('Incert Gutschein gekauft', 'incert-coupons');
        return $columns;
    }

    function custom_orders_new_column_content( $column_name, $order ) {
        if('coupons_used' === $column_name) {
            $coupons = $order->get_coupon_codes();

            if (!empty($coupons)) {
                echo esc_html(implode(', ', $coupons));
            } else {
                echo esc_html__('-', 'incert-coupons');
            }
        }

        if('incert_coupon_used' === $column_name) {
            $incertCouponUsed = get_post_meta($order->get_id(), '_incert_coupon_used', true);

            if ($incertCouponUsed) {
                echo esc_html__('Ja', 'incert-coupons');
            }
            else {
                echo esc_html__('Nein', 'incert-coupons');
            }
        }

        if('is_incert_order' === $column_name) {
            $hasIncertProduct = get_post_meta($order->get_id(), '_incert_has_incert_product', true);

            if ($hasIncertProduct) {
                echo esc_html__('Ja', 'incert-coupons');
            }
            else {
                echo esc_html__('Nein', 'incert-coupons');
            }
        }
    }

    public function custom_order_filter( $post_type, $which ) {
        if( 'shop_order' !== $post_type ) {
            return;
        }

        $incert_custom_field = isset( $_GET[ 'incert_custom_field' ] ) ? sanitize_text_field( wp_unslash( $_GET['incert_custom_field'] ) ) : '';

        ?>
        <select name="incert_custom_field">
            <option value="">
                <?php echo esc_html__('Kein Incert Filter', 'incert-coupons'); ?>
            </option>
            <option value="_incert_coupon_used" <?php selected( $incert_custom_field, '_incert_coupon_used' ) ?>>
                <?php echo esc_html__('Incert Gutschein eingelöst', 'incert-coupons'); ?>
            </option>
            <option value="_incert_has_incert_product" <?php selected( $incert_custom_field, '_incert_has_incert_product' ) ?>>
                <?php echo esc_html__('Incert Gutschein gekauft', 'incert-coupons'); ?>
            </option>
        </select>
        <?php
    }

    public function custom_order_filter_list($query_args) {
        $incert_custom_field = isset( $_GET[ 'incert_custom_field' ] ) ? sanitize_text_field( wp_unslash( $_GET['incert_custom_field'] ) ) : '';

        if( $incert_custom_field ) {
            $query_args[ 'meta_query' ] = array(
                array(
                    'key' => $incert_custom_field,
                    'compare' => '=',
                    'value' => '1',
                )
            );
        }

        return $query_args;
    }
}
