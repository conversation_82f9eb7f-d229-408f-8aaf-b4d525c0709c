#!/bin/bash

# This script deploys the plugin to the WordPress.org SVN repository
# Usage: ./deploy-to-svn.sh [tag]
# Usage: ./deploy-to-svn.sh --remove-tag <version>

# Exit if any command fails
set -e

# Main config
PLUGIN_SLUG="incert-coupons"
SVN_USER="incertapps" # Replace with your WordPress.org username or use environment variable
SVN_PASS="svn_4g2cjF1cVO26Q65mkM1TBdwkgol9oPUr2c60f53" # Use environment variable for security
SVN_URL="https://plugins.svn.wordpress.org/$PLUGIN_SLUG"
GIT_DIR="$(pwd)"
SVN_DIR="$GIT_DIR/svn-$PLUGIN_SLUG"
PLUGIN_VERSION=$(grep "Version:" "$GIT_DIR/$PLUGIN_SLUG/$PLUGIN_SLUG.php" | awk -F' ' '{print $NF}' | tr -d '\r')

# Function to remove a tag
remove_tag() {
    local tag_version="$1"

    if [[ -z "$tag_version" ]]; then
        echo "Error: No tag version specified for removal."
        echo "Usage: $0 --remove-tag <version>"
        exit 1
    fi

    echo "Preparing to remove tag: $tag_version"

    # Check if SVN directory exists
    if [[ -d "$SVN_DIR" ]]; then
        echo "SVN directory exists, updating..."
        cd "$SVN_DIR"
        svn update
    else
        echo "SVN directory does not exist, checking out..."
        svn checkout "$SVN_URL" "$SVN_DIR" --username="$SVN_USER" --password="$SVN_PASS"
        cd "$SVN_DIR"
    fi

    # Check if tag exists
    if ! svn ls "$SVN_URL/tags/$tag_version" > /dev/null 2>&1; then
        echo "Error: Tag $tag_version does not exist."
        exit 1
    fi

    # Confirm with the user
    echo "Ready to remove tag $tag_version from $PLUGIN_SLUG plugin SVN repository"
    echo "SVN username: $SVN_USER"
    read -p "Confirm tag removal? [y/N]: " CONFIRM

    if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
        echo "Tag removal aborted."
        exit 1
    fi

    # Remove the tag
    echo "Removing tag $tag_version..."
    svn rm "tags/$tag_version" --force

    # Commit the removal
    echo "Committing tag removal to SVN..."
    svn commit -m "Remove tag $tag_version" --username="$SVN_USER" --password="$SVN_PASS"

    echo "Tag $tag_version has been successfully removed!"
    exit 0
}

# Check for remove-tag option
if [[ "$1" == "--remove-tag" ]]; then
    if [[ -z "$2" ]]; then
        echo "Error: No tag version specified for removal."
        echo "Usage: $0 --remove-tag <version>"
        exit 1
    fi
    remove_tag "$2"
fi

# Check if SVN username and password are set
if [[ -z "$SVN_USER" || -z "$SVN_PASS" ]]; then
    echo "Error: SVN_USERNAME and/or SVN_PASSWORD environment variables are not set."
    echo "Please set them before running this script:"
    echo "export SVN_USERNAME='your_wp_username'"
    echo "export SVN_PASSWORD='your_wp_password'"
    exit 1
fi

# Confirm with the user
echo "Ready to deploy version $PLUGIN_VERSION of $PLUGIN_SLUG plugin to WordPress.org SVN"
echo "SVN username: $SVN_USER"
read -p "Confirm deployment? [y/N]: " CONFIRM

if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
    echo "Deployment aborted."
    exit 1
fi

# Check if SVN directory exists
if [[ -d "$SVN_DIR" ]]; then
    echo "SVN directory exists, updating..."
    cd "$SVN_DIR"
    svn update
else
    echo "SVN directory does not exist, checking out..."
    svn checkout "$SVN_URL" "$SVN_DIR" --username="$SVN_USER" --password="$SVN_PASS"
    cd "$SVN_DIR"
fi

# Make sure trunk directory exists
if [[ ! -d "$SVN_DIR/trunk" ]]; then
    echo "Creating trunk directory..."
    mkdir -p "$SVN_DIR/trunk"
    svn add trunk
fi

# Clean up SVN trunk
echo "Cleaning SVN trunk..."
# Use find to remove files instead of svn rm with wildcard
find "$SVN_DIR/trunk" -type f -exec svn rm --force {} \;

# Copy Git files to SVN trunk (excluding vendor directory)
echo "Copying files from Git to SVN trunk..."
rsync -av --exclude='vendor/' "$GIT_DIR/$PLUGIN_SLUG"/ "$SVN_DIR/trunk/"

# Check if assets directory exists in Git
if [[ -d "$GIT_DIR/$PLUGIN_SLUG/assets" ]]; then
    echo "Copying assets..."
    # Remove existing assets in SVN
    if [[ -d "$SVN_DIR/assets" ]]; then
        svn rm --force assets/* --quiet
    else
        mkdir -p assets
    fi
    cp -R "$GIT_DIR/$PLUGIN_SLUG/assets"/* assets/
fi

# Add all new files to SVN
echo "Adding new files to SVN..."
svn status | grep '^\?' | awk '{print $2}' | xargs -I% svn add %

# Remove deleted files from SVN
echo "Removing deleted files from SVN..."
svn status | grep '^\!' | awk '{print $2}' | xargs -I% svn rm %

# Check if tag should be created
if [[ -n "$1" ]]; then
    TAG="$1"
    echo "Creating new SVN tag: $TAG"

    # Check if tag already exists
    if svn ls "$SVN_URL/tags/$TAG" > /dev/null 2>&1; then
        echo "Tag $TAG already exists. Exiting..."
        exit 1
    fi

    # Create the tag
    svn cp trunk "tags/$TAG"
fi

# Commit changes
echo "Committing changes to SVN..."
if [[ -n "$1" ]]; then
    svn commit -m "Release $TAG" --username="$SVN_USER" --password="$SVN_PASS"
else
    svn commit -m "Update trunk" --username="$SVN_USER" --password="$SVN_PASS"
fi

echo "SVN deployment complete!"
exit 0
