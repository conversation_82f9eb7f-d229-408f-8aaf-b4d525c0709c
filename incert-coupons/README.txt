=== incert Voucher Management System ===
Contributors: incertat
Donate link: https://www.incert.at
Tags: woocommerce, vouchers, coupons, api, incert, pdf, pkpass, fulfillment
Requires at least: 6.4
Tested up to: 6.7
Requires PHP: 7.4
Stable tag: 1.0.3
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Professioneller Gutscheinverkauf und -einlösung in WooCommerce – powered by INCERT.

== Description ==

Are you already selling vouchers in your WooCommerce shop? If so, you may have noticed that WooCommerce offers only a basic, somewhat unprofessional approach for handling vouchers. Vouchers can only be sold as simple items, which limits smooth and comprehensive management, particularly when it comes to bookkeeping compliance and customer satisfaction.

**Key features are missing, such as:**
* Automatic Digital Voucher creation with dynamic voucher code generation
* Voucher personalization with custom designs, image uploads, and dedication texts
* Automatic generation of digital vouchers as PDF, QR Code, or PKPass format (Wallet)
* Automatic Fulfillment of ordered vouchers via e-mail or postal delivery
* Voucher Redemption in the WooCommerce Cart

Connect your WooCommerce shop now with a professional voucher system from INCERT to automate and enhance voucher sales, redemption, and management. This allows you to elevate voucher revenue and streamline processes on all fronts.

**Key Benefits**
* Sale of digital vouchers in your WooCommerce shop via API fulfillment
* Custom landing pages for optimized voucher sales through INCERT sales interfaces, with personalization options like voucher templates, image, text, and video customization
* Sale of digital vouchers as PDF, QR Code, or PKPASS for wallet storage
* Sale of physical vouchers such as gift cards or printed vouchers with packaging
* Lettershop integration for automated production and worldwide shipping
* Advanced admin panel for voucher management with a professional dashboard, analytics, and reporting for accounting
* Voucher redemption in the WooCommerce cart, desktop, or INCERT app
* Integration with accounting, CRM, PMS, POS, or access control systems

== Installation ==

1. Upload the plugin to the `/wp-content/plugins/` directory.
2. Activate the plugin through the "Plugins" menu in WordPress.
3. Go to the plugin settings and enter your INCERT API credentials.
4. Link your WooCommerce products with INCERT voucher templates.

== Frequently Asked Questions ==

= Do I need an INCERT account? =
Yes. A valid INCERT account and API access are required. Contact [https://www.incert.at](https://www.incert.at) to get started.

= What if I don’t have API credentials yet? =
Reach out to INCERT for onboarding and credential provisioning.

== Screenshots ==

1. Plugin settings with API integration (Einstellungen mit API-Verbindung)
2. Voucher product with personalization fields (Gutscheinprodukt mit Personalisierung)
3. PDF voucher example with QR code (PDF-Gutschein mit QR-Code)
4. Admin dashboard with reporting and BUHA (Admin-Panel mit Auswertungen)
5. Format selection in WooCommerce product (Formatauswahl im Produkt)
6. Voucher redemption in WooCommerce cart (Einlösung im Warenkorb)

== Changelog ==

=== English ===

= 1.0.3 =
* Improved API integration
* Enhanced WooCommerce compatibility
* Bug fixes and performance improvements

= 1.0.2 =
* Fix API

= 1.0.0 =
* Initial release

== Upgrade Notice ==

= 1.0.3 =
This update improves API integration, enhances WooCommerce compatibility, and includes important bug fixes and performance improvements.

= 1.0.2 =
Fixed minor API issues and improved WooCommerce compatibility.

== Support ==

Not an INCERT customer yet or missing API credentials?
→ Visit [https://www.incert.at](https://www.incert.at) or get in touch via our contact form.
