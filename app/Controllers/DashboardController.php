<?php

namespace App\Controllers;

use App\Controllers\Controller;
use App\Models\User;

class DashboardController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();

        // Check if user is logged in
        if (!isset($_SESSION['user'])) {
            $this->redirect('/login');
        }
    }

    /**
     * Show the dashboard
     *
     * @return void
     */
    public function index()
    {
        // Get user data
        $user = User::find($_SESSION['user']['id']);

        // Get subscription data if exists
        $subscription = $user->subscription;

        $this->render('dashboard/index', [
            'user' => $user,
            'subscription' => $subscription,
        ]);
    }

    /**
     * Show the profile page
     *
     * @return void
     */
    public function profile()
    {
        // Get user data
        $user = User::find($_SESSION['user']['id']);

        $this->render('dashboard/profile', [
            'user' => $user,
        ]);
    }

    /**
     * Update the user profile
     *
     * @return void
     */
    public function updateProfile()
    {
        // Get user data
        $user = User::find($_SESSION['user']['id']);

        // Get form data
        $name = $this->input('name');
        $firstName = $this->input('first_name');
        $lastName = $this->input('last_name');
        $email = $this->input('email');
        $company = $this->input('company');
        $phone = $this->input('phone');
        $currentPassword = $this->input('current_password');
        $newPassword = $this->input('new_password');
        $newPasswordConfirmation = $this->input('new_password_confirmation');

        // Validate input
        if (empty($firstName) || empty($lastName) || empty($email)) {
            $_SESSION['error'] = 'First name, last name, and email are required';
            $this->redirect('/profile');
            return;
        }

        // Make sure name is set (should be set by JavaScript, but just in case)
        if (empty($name)) {
            $name = $firstName . ' ' . $lastName;
        }

        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $_SESSION['error'] = 'Invalid email address';
            $this->redirect('/profile');
            return;
        }

        // Check if email is already taken by another user
        if ($email !== $user->email && User::where('email', $email)->exists()) {
            $_SESSION['error'] = 'Email already exists';
            $this->redirect('/profile');
            return;
        }

        // Update user data
        $user->name = $name;
        $user->first_name = $firstName;
        $user->last_name = $lastName;
        $user->email = $email;
        $user->company = $company;
        $user->phone = $phone;

        // Update password if provided
        if (!empty($currentPassword) && !empty($newPassword)) {
            // Validate current password
            if (!password_verify($currentPassword, $user->password)) {
                $_SESSION['error'] = 'Current password is incorrect';
                $this->redirect('/profile');
                return;
            }

            // Validate new password
            if (strlen($newPassword) < 8) {
                $_SESSION['error'] = 'New password must be at least 8 characters';
                $this->redirect('/profile');
                return;
            }

            // Validate password confirmation
            if ($newPassword !== $newPasswordConfirmation) {
                $_SESSION['error'] = 'New passwords do not match';
                $this->redirect('/profile');
                return;
            }

            // Update password
            $user->password = $newPassword;
        }

        // Save changes
        $user->save();

        // Update session
        $_SESSION['user']['name'] = $user->name;
        $_SESSION['user']['email'] = $user->email;

        $_SESSION['success'] = 'Profile updated successfully';
        $this->redirect('/profile');
    }
}
