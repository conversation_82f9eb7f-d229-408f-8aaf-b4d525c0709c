<?php
/**
 * Custom template to add login icon in header.
 */
?>
<?php
    $labelLogIn = $block->getLabelLogIn();
    $labelLogOut = $block->getLabelLogOut();
    $customerHelper = $this->helper(\MageSuite\Frontend\Helper\Customer::class);
    $isLoggedIn = $customerHelper->isLoggedIn();
?>

<li class="cs-header-user-nav__item <?= $block->getAdditionalCssClasses() ?><?php if ($isLoggedIn): ?> cs-header-user-nav__item--logged-in<?php endif; ?>">
    <a href="<?= $block->escapeUrl($block->getHref()); ?>" class="cs-header-user-nav__link" <?= /* @noEscape */ $block->getLinkAttributes() ?>>
        <div class="cs-header-user-nav__icon-wrapper">
            <?php if ($isLoggedIn): ?>
                <?= $block->getChildHtml('my-account-link.icon.logged-in'); ?>
            <?php else: ?>
                <?= $block->getChildHtml('my-account-link.icon.logged-out'); ?>
            <?php endif; ?>
        </div>
        <?php if ($isLoggedIn): ?>
            <?php if (!empty($labelLogIn)): ?>
                <span class="cs-header-user-nav__label"><?= $labelLogIn ?></span>
            <?php endif; ?>
        <?php else: ?>
            <?php if (!empty($labelLogOut)): ?>
                <span class="cs-header-user-nav__label"><?= $labelLogOut ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </a>
</li>
