<?php
// @codingStandardsIgnoreFile
/** @var \Aheadworks\Faq\Block\Category\CategoryList $block */
?>

<div class="cs-faq-index__container">
<?php foreach($block->getCategories() as $category): ?>
    <?php $categoryArticles = $block->getCategoryArticles($category); ?>
    <?php $allowedArticlesNumber = $category->getNumArticlesToDisplay(); ?>
    <?php $moreArticles = count($categoryArticles) - $allowedArticlesNumber; ?>
    <?php if ($category->getArticleListIcon()) {
         $articleListIcon = 'class="cs-faq-index__list-item cs-faq-index__list-item-img" style="background:url(' . $block->escapeUrl($block->getArticleListIconUrl($category)) . ')"';
    } else {
         $articleListIcon = 'class="cs-faq-index__list-item"';
    }
    ?>
    <div class="cs-faq-index__category">
        <section class="cs-faq-index__category-section" data-mage-init='{
                "collapsible": {
                    "mediaQueryScope": "(max-width: 767px)"
                }
            }'>
            <h2 class="cs-faq-index__title cs-faq-index__category-name" data-role="title">
                <?php if ($block->getCategoryIconUrl($category)) :?>
                    <img class="cs-faq-index__list-img" src="<?php echo $block->escapeUrl($block->getCategoryIconUrl($category)); ?>" alt="<?php echo $block->escapeHtml($category->getName()); ?>" />
                <?php endif; ?>
                <a href="<?php echo $block->escapeUrl($block->getCategoryUrl($category)); ?>"><?php echo $block->escapeHtml($category->getName()); ?></a>
            </h2>
            <ul class="cs-faq-index__list" data-role="content">
                <?php if ($allowedArticlesNumber): ?>
                    <?php while($allowedArticlesNumber): ?>
                        <?php if (isset($categoryArticles[$allowedArticlesNumber-1])): ?>
                            <?php $article = $categoryArticles[$allowedArticlesNumber-1]; ?>
                            <li <?php /* @noEscape */ echo $articleListIcon ?> >
                                <a href="<?php echo $block->escapeUrl($block->getArticleUrl($article)); ?>"><?php echo  $block->escapeHtml($article->getTitle()); ?>
                                    <?= $block->getChildHtml('aw_faq.category_list.icon'); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php $allowedArticlesNumber--; ?>
                    <?php endwhile; ?>
                <?php else: ?>
                    <?php foreach ($categoryArticles as $article): ?>
                        <li <?php /* @noEscape */ echo $articleListIcon ?> >
                            <a href="<?php echo $block->escapeUrl($block->getArticleUrl($article)); ?>"><?php echo  $block->escapeHtml($article->getTitle()); ?>
                                <?= $block->getChildHtml('aw_faq.category_list.icon'); ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php endif; ?>
            </ul>
            <?php if ($category->getNumArticlesToDisplay() && $moreArticles > 0):?>
                <a class="cs-faq-index__more-link" href="<?php echo $block->escapeUrl($block->getCategoryUrl($category)); ?>">
                    <?php echo $moreArticles == 1 ? __('Read 1 more article') : __('Read %1 more articles', $moreArticles) ?>
                </a>
            <?php endif; ?>
        </section>
    </div>
<?php endforeach ?>
</div>
