<?php
// @codingStandardsIgnoreFile
/** @var \Magento\Framework\View\Element\Template $block */
$level = $block->getLevel();
$arrowIcon = $block->getArrowIcon();
$showCategoryIconSubcats = $block->getShowCategoryIconSubcats();

$item = $block->getItem();
$itemUrl = $item->getUrl();
$itemLabel = $item->getLabel();
$itemIdentifier = $item->getIdentifier() ?? '';
?>
<ul class="cs-side-navigation__list cs-side-navigation__list--level_<?= $level; ?>">
    <li class="cs-side-navigation__item cs-side-navigation__item--all-products">
        <a class="cs-side-navigation__link" href="<?= $itemUrl; ?>"><?= __('All products'); ?></a>
    </li>
<?php
foreach($item->getSubItems() as $subItem):
    $subItem = $subItem;
    $subItemId = $subItem->getId();
    $subItemUrl = $subItem->getUrl();
    $subItemLabel = $subItem->getLabel();
    $subItemIdentifier = $subItem->getIdentifier() ?? '';
    $subItemHasSubItems = $subItem->hasSubItems();
    $subItemCategoryIcon = $showCategoryIconSubcats ? $subItem->getCategoryIcon() : '';
    $itemNextAdditionalClasses = '';

    if ($subItemHasSubItems) {
        $itemNextAdditionalClasses .= ' cs-side-navigation__link--parent';

        if (!$arrowIcon) {
            $itemNextAdditionalClasses .= '  cs-side-navigation__link--next';
        }
    }
?>
    <li class="cs-side-navigation__item cs-side-navigation__item--level_<?= $level; ?>"<?php if($subItemIdentifier): ?> data-category-identifier="<?= $subItemIdentifier; ?>"<?php endif; ?>>
        <a
            class="cs-side-navigation__link<?= $itemNextAdditionalClasses ?>"
            href="<?= $subItemUrl; ?>"
            data-category-id="<?= $subItemId ?>"
        >
            <?php if ($subItemHasSubItems && $arrowIcon) : ?>
                <?= $arrowIcon ?>
            <?php endif; ?>
            <?php if ($subItemCategoryIcon): ?>
                <span class="cs-side-navigation__category-icon-wrapper">
                    <img src="<?= $subItemCategoryIcon; ?>" alt="<?= $subItemLabel; ?>" class="cs-side-navigation__category-icon">
                </span>
            <?php endif; ?>
            <span class="cs-side-navigation__text">
                <?= $subItemLabel; ?>
            </span>
        </a>
        <?php
            if ($subItemHasSubItems) {
                echo $this->getLayout()
                    ->createBlock(Magento\Framework\View\Element\Template::class)
                    ->setTemplate("MageSuite_Navigation::sidenav/list.phtml")
                    ->setItem($subItem)
                    ->setLevel($level + 1)
                    ->setArrowIcon($block->getArrowIcon())
                    ->setShowCategoryIconSubcats($showCategoryIconSubcats)
                    ->toHtml();
            }
        ?>
    </li>
<?php endforeach; ?>
</ul>
