
.checkout-index-index span,
.checkout-index-index select,
.checkout-index-index td,
.checkout-index-index input,
.checkout-index-index th {
    font-size: 16px;
}

.checkout-index-index .shipping-information,
.checkout-index-index .checkout-billing-address,
.checkout-index-index .payment-method-title {
    font-size: 14px;
}

.checkout-index-index .payment-method-title span,
.checkout-index-index .billing-address-same-as-shipping-block span {
    color: #333333;
}

.checkout-index-index .authentication-wrapper {
    margin-top: -45px;
}

#shipping-method-buttons-container {
    align-items: unset;
    justify-content: unset;
}

.checkout-index-index select {
    border: solid 1px #c2c2c2;
}


.checkout-index-index .minicart-items .product-image-container {
    float: unset;
}

.checkout-index-index .minicart-items .product-item-details {
    margin-top: 20px;
    padding-left: unset;
}

.checkout-index-index .opc-summary-wrapper {
    margin-top: unset;
}

.checkout-index-index .minicart-items .product-image-wrapper {
    width: max-content;
}

.checkout-index-index .message.notice {
    color: white;
}
