// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Pager variables
//  _____________________________________________

@pager-label__display: none;
@pager-reset-spaces: true; // Reset spaces between inline-block elements

@pager__font-size: @font-size__s;
@pager__font-weight: @font-weight__bold;
@pager__line-height: 32px;

@pager-item__display: inline-block;
@pager-item__margin: 0 2px 0 0;
@pager-item__padding: 0 4px;

@pager-actions__padding: 0;

//  Pager current page
@pager-current__font-weight: @font-weight__bold;
@pager-current__color: @primary__color;
@pager-current__border: false;
@pager-current__background: false;
@pager-current__gradient: false;
@pager-current__gradient-direction: false;
@pager-current__gradient-color-start: false;
@pager-current__gradient-color-end: false;

//  Pager link page
@pager__gradient: false;
@pager__gradient-direction: false;

//  Pager link default
@pager__color: @link__color;
@pager__border: false;
@pager__text-decoration: none;
@pager__background: false;
@pager__gradient-color-start: false;
@pager__gradient-color-end: false;

//  Pager link visited
@pager__visited__color: @link__visited__color;
@pager__visited__border: false;
@pager__visited__background: false;
@pager__visited__gradient-color-start: false;
@pager__visited__gradient-color-end: false;

//  Pager link hover
@pager__hover__color: @link__hover__color;
@pager__hover__border: false;
@pager__hover__text-decoration: none;
@pager__hover__background: false;
@pager__hover__gradient-color-start: false;
@pager__hover__gradient-color-end: false;

//  Pager link active
@pager__active__color: @link__active__color;
@pager__active__border: false;
@pager__active__background: false;
@pager__active__gradient-color-start: false;
@pager__active__gradient-color-end: false;

//  Pager link.action
@pager-icon__use: true;
@pager-icon__previous-content: @icon-prev;
@pager-icon__next-content: @icon-next;
@pager-icon__text-hide: true;
@pager-icon__position: before;
@pager-icon__font: @icon-font;
@pager-icon__font-margin: 0 0 0 -6px;
@pager-icon__font-vertical-align: top;
@pager-icon__font-size: 46px;
@pager-icon__font-line-height: @icon-font__line-height;

//  Pager link.action gradient: element has a gradient background
@pager-action__gradient: false; // [true|false]
@pager-action__gradient-direction: false; // [true|false]

//  Pager link.action default
@pager-action__color: @text__color__muted;
@pager-action__border: @border-width__base solid @border-color__base;
@pager-action__text-decoration: @pager__text-decoration;
@pager-action__background: @pager__background;
@pager-action__gradient-color-start: false;
@pager-action__gradient-color-end: false;

//  Pager link.action visited
@pager-action__visited__color: @pager-action__color;
@pager-action__visited__border: false;
@pager-action__visited__background: false;
@pager-action__visited__gradient-color-start: false;
@pager-action__visited__gradient-color-end: false;

//  Pager link.action hover
@pager-action__hover__color: @pager-action__color;
@pager-action__hover__border: false;
@pager-action__hover__background: false;
@pager-action__hover__text-decoration: @pager__hover__text-decoration;
@pager-action__hover__gradient-color-start: false;
@pager-action__hover__gradient-color-end: false;

//  Pager link.action active
@pager-action__active__color: @pager-action__color;
@pager-action__active__border: false;
@pager-action__active__background: false;
@pager-action__active__gradient-color-start: false;
@pager-action__active__gradient-color-end: false;
