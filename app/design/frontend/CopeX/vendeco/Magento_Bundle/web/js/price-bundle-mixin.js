define([
    'jquery'
], function ($) {
    'use strict';

    return function (priceBundle) {
        $.widget('mage.priceBundle', priceBundle, {
            options: {
                optionTemplate: '<%- data.label %>' +
                    '<% if (data.finalPrice.value) { %>' +
                    ' +<%- data.finalPrice.value %>' +
                    '<% } %>'
            }
        });
        return $.mage.priceBundle;
    };
});