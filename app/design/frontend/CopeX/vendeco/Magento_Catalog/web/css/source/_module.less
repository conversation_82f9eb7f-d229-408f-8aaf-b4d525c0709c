// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@product-grid-items-per-row-layout-default: 2;

@product-grid-items-per-row-layout-1-screen-s: 3;
@product-grid-items-per-row-layout-1-screen-m: 4;
@product-grid-items-per-row-layout-1-screen-l: 5;

@product-grid-items-per-row-layout-2-left-screen-s: 3;
@product-grid-items-per-row-layout-2-left-screen-m: 4;
@product-grid-items-per-row-layout-2-left-screen-l: '';

@product-grid-items-per-row-layout-2-right-screen-s: 3;
@product-grid-items-per-row-layout-2-right-screen-m: 4;
@product-grid-items-per-row-layout-2-right-screen-l: '';

@product-grid-items-per-row-layout-3-screen-s: 3;
@product-grid-items-per-row-layout-3-screen-m: '';
@product-grid-items-per-row-layout-3-screen-l: '';

@product-grid-items-padding: 0 @indent__base @indent__base;
@product-grid-items-margin: 0 0 @indent__s;

@product-name-text-decoration: none;
@product-name-text-decoration-hover: @link__hover__text-decoration;

@toolbar-mode-icon-font-size: 26px;
@product-h1-margin-bottom-desktop: @indent__base;

@import 'module/_listings.less';
@import 'module/_toolbar.less';

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Category view
    //  ---------------------------------------------

    .old-price,
    .old.price {
        text-decoration: line-through;
    }

    .prices-tier {
        .price-container {
            .price-including-tax {
                + .price-excluding-tax {
                    &:before {
                        content: '(' attr(data-label) ': ';
                    }

                    &:last-child:after {
                        content: ')';
                    }
                }
            }

            .weee[data-label] {
                display: inline;

                .price {
                    .lib-font-size(11);
                }

                &:before {
                    content: ' +' attr(data-label) ': ';
                }
            }
        }
    }

    .actual-price {
        font-weight: @font-weight__bold;
    }

    .product.name a {
        &:extend(.abs-product-link all);
    }

    .category-image {
        .image {
            display: block;
            height: auto;
            max-width: 100%;
        }
    }

    .category-image,
    .category-description {
        margin-bottom: @indent__base;
    }

    //
    //  Product images general container
    //  ---------------------------------------------

    .product-image-container {
        display: inline-block;
        max-width: 100%;
    }

    .product-image-wrapper {
        display: block;
        height: 0;
        overflow: hidden;
        position: relative;
        z-index: 1;
    }

    .product-image-photo {
        bottom: 0;
        display: block;
        height: auto;
        left: 0;
        margin: auto;
        max-width: 100%;
        position: absolute;
        right: 0;
        top: 0;
    }

    //
    //  Product view
    //  ---------------------------------------------

    .product.media {
        .product.photo .photo.image {
            &:extend(.abs-adaptive-images-centered);
        }

        .placeholder .photo.container {
            max-width: 100%;
        }

        .gallery-placeholder .gallery__stage .gallery__img {
            padding-bottom: 100%;
        }

        .notice {
            .lib-css(color, @text__color__muted);
            .lib-font-size(@font-size__s);
            margin: @indent__s 0;
        }

        .product.thumbs {
            margin: @indent__base 0 @indent__l;
        }

        .items.thumbs {
            .lib-list-inline();

            .active {
                display: block;
                line-height: 1;
            }
        }

        #previousPageButton {
            display: inline-block;
            margin-top: 20px;
            padding-left: 20px;
            position: relative;

            &:before {
                content: '\f30a';
                margin-top: 1px;
                font-family: @icons__font-awesome;
                font-weight: @icons__solid;
                position: absolute;
                left: 0;
            }
        }
    }

    .product.extra-info {
        overflow: hidden;
        width: 100%;

        .widget-info-box {
            .template-second {
                margin-bottom: 0 !important;
            }
        }

        .mod-row2 {
            margin-bottom: 35px;
        }
    }

    .sections.anchors-wrapper a > .scroll-progress-bar {
        display: block;
        position: absolute;
        background: @seashell;
        top: 0;
        bottom: 0;
        left: 0;
        width: 0;
        opacity: 1;

        &[data-state="completed"] {
            + span {
                color: @blue;
            }
        }

        &:before {
            content: "";
            background: @blue;
            display: block;
            position: relative;
            top: 0;
            width: 100%;
            height: 5px;
            left: 0;
        }
    }

    .additional-attributes-sections {
        .sticky-wrapper[data-state="stuck"] {
            .anchors-wrapper {
                margin-top: 0;
            }
        }

        .sticky-wrapper[data-state="started"] {
            z-index: 99 !important;
        }

        .anchors-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            margin: 0 -2px 0;
            padding: 10px 0 0;
            background: white;

            @media (max-width: 767px) {
                flex-flow: column nowrap;
                display: none;
            }

            .item {
                width: 11.2%;
                padding: 0 2px;

                @media (max-width: 767px) {
                    width: 100%;
                    margin-bottom: 15px;
                }

                > a {
                    background: @light-grey;
                    text-align: center;
                    display: flex !important;
                    align-items: center;
                    justify-content: center;
                    padding: 5px;
                    height: 60px;
                    line-height: 16px;
                    font-size: 15px;
                    color: @blue;
                    font-family: @font-family__base;
                    word-break: break-word;
                    box-sizing: border-box;
                    position: relative;
                    overflow: hidden;
                    font-weight: @font-weight__regular;
                    transition: all 0.3s ease-in-out;

                    @media (min-width: 767px) and (max-width: 1023px) {
                        font-size: 12px;
                        line-height: 14px;
                        padding: 10px 5px;
                    }

                    @media (min-width: 1024px) and (max-width: 1129px) {
                        font-size: 14px;
                        line-height: 15px;
                        padding: 7px 5px;
                    }

                    > span {
                        display: block;
                        z-index: 1;
                    }

                    &:focus,
                    &:active,
                    &:hover {
                        background: @seashell;
                        text-decoration: none !important;

                        &:before {
                            content: "";
                            background: @blue;
                            display: block;
                            position: absolute;
                            top: 0;
                            width: 100%;
                            height: 5px;
                            left: 0;
                            z-index: 2;
                        }
                    }
                }
            }
        }

        .contents-wrapper {
            .lib-ul();

            > .item {
                > h2 {
                    margin-top: 70px;
                }
            }
        }
    }

    .product.info.detailed {
        clear: both;
        margin-bottom: 30px;

        .additional-attributes {
            width: auto;
            .lib-table-resize(
                @_th-padding-left: 0,
                @_th-padding-right: @indent__l,
                @_th-padding-bottom: @indent__s,
                @_td-padding-bottom: @indent__s
            );
        }
    }

    .product-info-main {
        .page-title-wrapper {
            .page-title {
                margin-bottom: 0;
                margin-top: -7px;
                font-size: 20px;
                line-height: 30px;
                font-weight: @font-weight__bold;
                font-family: @font-family__base;
                color: @text__color;
            }
        }

        .stock {
            &.available,
            &.unavailable {
                display: inline-block;
                font-weight: @font-weight__bold;
                margin-right: @indent__base;
                text-transform: uppercase;
                vertical-align: top;
            }
        }

        .product {
            &.attribute {
                &.sku {
                    display: inline-block;
                    vertical-align: top;
                    .lib-css(color, @text__color__muted);

                    > .value {
                        display: inline-block;
                        vertical-align: top;
                        word-break: break-all;
                    }

                    .type {
                        margin-right: @indent__xs;
                    }
                }

                &.overview {
                    margin: @indent__base 0;
                }
            }

            &.alert {
                margin: @indent__s 0;
            }

            &.description {
                font-size: 18px;
                line-height: 30px;
                margin-bottom: 6px;
            }
        }

        .product-info-price {
            margin-bottom: 30px;
        }

        .price-box {
            margin-top: 0;
            flex-flow: row wrap;
            display: inline-flex;
        }

        .price-details {
            display: inline;
        }

        .product-reviews-summary .reviews-actions {
            .lib-font-size(@font-size__base);
        }
    }

    .product-options-wrapper {
        .fieldset-product-options-inner {
            .legend {
                .lib-css(font-weight, 400);
                .lib-css(margin, 0 0 @indent__xs);
                .lib-font-size(14px);
                border: none;
                display: inline-block;
                float: none;
                padding: 0;
                text-transform: uppercase;

                > span {
                    &:after {
                        content: '*';
                        color: @red;
                        font-size: 1.2rem;
                        margin: 0 0 0 5px;
                    }
                }

                .price-notice {
                    margin-left: 7px;

                    .tax.weee {
                        font-size: 11px;
                    }

                    &:after {
                        display: none;
                    }
                }

                &:after {
                    display: none;
                }
            }

            //  Date & Time custom option (Affect Time that goes only after Date)
            input.datetime-picker {
                ~ select.datetime-picker {
                    margin-top: @indent__s;
                }
            }

            &.required,
            &._required {
                .legend {
                    &:after {
                        content: '*';
                        .lib-typography(
                            @_font-size: @form-field-label-asterisk__font-size,
                            @_color: @form-field-label-asterisk__color,
                            @_font-family: @form-field-label-asterisk__font-family,
                            @_font-weight: @form-field-label-asterisk__font-weight,
                            @_line-height: @form-field-label-asterisk__line-height,
                            @_font-style: @form-field-label-asterisk__font-style
                        );
                        .lib-css(margin, @form-field-label-asterisk__margin);
                    }
                }
            }
        }

        .field {
            .note {
                display: block;
            }

            .price-notice {
                &:extend(.abs-adjustment-incl-excl-tax all);
            }
        }
    }

    .product-info-main,
    .product-options-bottom {
        .price-box {
            .price-including-tax + .price-excluding-tax,
            .weee + .price-excluding-tax,
            .weee {
                .lib-font-size(12);
                line-height: 14px;
                margin-bottom: 0;
            }

            .price-label {
                display: none !important;
            }

            .price {
                white-space: nowrap;
            }
        }

        .special-price {
            display: block;
            margin: @indent__s 0;

            .price-container {
                .lib-font-size(14);
            }

            .price-label + .price-wrapper {
                display: inline-block;
            }
        }

        .old-price,
        .special-price {
            .price-label {
                &:after {
                    content: ': ';
                }
            }
        }

        .box-tocart {
            margin: 0;

            .fieldset {
                .field:not(.choice) {
                    > label.label {
                        position: relative;
                        left: 0;
                        top: 0;
                        padding: 0;
                        margin-bottom: 5px;
                        font-size: 15px;
                        line-height: 20px;
                        font-weight: @font-weight__regular;
                        text-transform: none;
                        background: transparent;
                    }
                }

                .field.qty.processed {
                    > label.label {
                        padding: 0 !important;
                    }
                }

                &:last-child {
                    margin: 0;
                }
            }

            .field.qty {
                margin-bottom: 35px;

                div.mage-error {
                    position: absolute;
                    margin-top: 0;
                    padding-top: 4px;
                    min-width: 250px;
                }

                label {
                    color: @text__color;
                }
            }

            .input-text.qty {
                height: 35px;
                width: 35px;
                text-align: center;
                font-size: 14px;

                &.valid,
                &.mage-error {
                    background-image: none !important;
                }
            }

            .action.tocart {
                padding: 15px 70px !important;
            }
        }

        .product-addto-links {
            margin: @indent__base 0;
        }
    }

    .prices-tier {
        &:extend(.abs-reset-list all);
        .lib-css(background, @sidebar__background-color);
        margin: @indent__s 0;
        padding: @indent__s (.75 * @indent__base);

        .price-container {
            display: inline-block;
        }

        .price-including-tax,
        .price-excluding-tax,
        .weee {
            display: inline-block;

            .price {
                .lib-font-size(14);
                font-weight: @font-weight__bold;
            }
        }
    }

    .ui-dialog-titlebar-close {
        .lib-button-as-link();
    }

    .block.related {
        .action.select {
            margin: 0 @indent__xs;
        }
    }

    //
    //  Sidebar product view
    //  ---------------------------------------------

    .sidebar {
        .product-items {
            .product-item {
                margin-bottom: @indent__base;
                position: relative;
            }

            .product-item-info {
                position: relative;
                width: auto;

                .product-item-photo {
                    left: 0;
                    position: absolute;
                    top: 0;
                }
            }

            .product-item-name {
                margin-top: 0;
            }

            .product-item-details {
                margin: 0 0 0 85px;
            }

            .product-item-actions {
                display: block;
                margin-top: @indent__s;
            }

            .price-box {
                display: block;
                margin: 7px 0;
            }

            .text {
                margin-right: 8px;
            }

            .counter {
                .lib-css(color, @primary__color__lighter);
                .lib-font-size(12);
                white-space: nowrap;
            }

            .minilist {
                .price {
                    display: inline;
                    padding: 0;
                }

                .weee:before {
                    display: inline-block;
                }
            }
        }

        .action {
            &.delete {
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        .subtitle {
            &:extend(.abs-no-display all);
        }

        //
        //  Product images only
        //  ---------------------------------------------

        .product-items-images {
            &:extend(.abs-add-clearfix all);
            margin-left: -@indent__xs;

            .product-item {
                &:extend(.abs-add-box-sizing all);
                float: left;
                padding-left: @indent__xs;
            }
        }

        //
        //  Product names only
        //  ---------------------------------------------

        .product-items-names {
            .product-item {
                display: flex;
                margin-bottom: @indent__s;
            }

            .product-item-name {
                margin: 0;
            }
        }
    }

    //
    //  Category page 1 column layout
    //  ---------------------------------------------

    .catalog-category-view.page-layout-1column {
        .column.main {
            min-height: inherit;
        }
    }

    //
    //  Category page 1 column layout
    //  ---------------------------------------------

    .catalog-category-view.page-layout-1column {
        .column.main {
            min-height: inherit;
        }
    }

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .catalog-product-view {
        .column.main {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction(column);
        }

        .product.media {
            .lib-vendor-prefix-order(-1);
            margin-bottom: 30px;
        }
    }

    .block.related {
        .action.select {
            display: block;
            margin: @indent__xs 0;
        }
    }

    .compare,
    .product-addto-links .action.tocompare,
    .product-item-actions .actions-secondary > .action.tocompare,
    [class*='block-compare'] {
        display: none;
    }

    .page-layout-1column {
        .product-info-main {
            margin-bottom: 20px;
        }
    }
    &.update {
        label.label {
            display: none !important;
        }

        .actions {
            padding-top: 0;

            #product-updatecart-button {
                margin-bottom: 20px;
                min-width: 244px;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-info-main,
    .product-options-bottom {
        .box-tocart {
            margin-top: 0;

            .field.qty {
                display: block;
            }

            .actions {
                display: block;
            }

            &.update {
                label.label {
                    display: none;
                }

                .actions {
                    padding-top: 0;

                    #product-updatecart-button {
                        margin-bottom: 20px;
                        min-width: 244px;
                    }
                }
            }
        }
    }

    .sidebar {
        .product-items {
            .product-item-info {
                .product-item-photo {
                    float: left;
                    left: auto;
                    margin: 0 @indent__s @indent__s 0;
                    position: relative;
                    top: auto;
                }
            }

            .product-item-details {
                margin: 0;
            }

            .product-item-actions {
                clear: left;
            }
        }
    }

    .product-add-form {
        &:extend(.abs-revert-field-type-desktop all);
    }
}

//
//  Desktop large
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .sidebar {
        .product-items {
            .product-item-info {
                .product-item-photo {
                    float: none;
                    left: 0;
                    margin: 0;
                    position: absolute;
                    top: 0;
                }
            }

            .product-item-details {
                margin-left: 85px;
            }
        }
    }
}

@media only screen and (min-width: 768px) and (max-width: 1023px) {
    .product-info-main {
        .product.rating {
            .avarageRating {
                .first {
                    flex-flow: column nowrap;

                    .star {
                        margin-bottom: 5px;
                    }
                }
            }
        }
    }
}

//
//  Category page layout
//  ---------------------------------------------

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-info-main {
        float: right;

        .product.rating {
            margin-bottom: 10px;

            .avarageRating {
                .first {
                    display: flex;
                    align-items: flex-start;

                    .star {
                        margin-right: 5px;
                    }
                }
            }
        }
    }

    .product.media {
        float: left;
    }

    .page-layout-1column {
        .product-info-main {
            width: 34%;
            margin-bottom: 30px;
        }

        .product.media {
            width: 66%;
            padding-right: 30px;
            box-sizing: border-box;

            .fotorama__nav-wrap {
                .fotorama_horizontal_ratio .fotorama__img {
                    width: 100%;
                }
            }

            .fotorama__nav__frame--thumb {
                opacity: 0.5;
                box-sizing: border-box;

                &.fotorama__active {
                    opacity: 1;
                }
            }

            .fotorama__thumb-border {
                border: none;
                margin: 0;
                display: none;
            }

            .fotorama__arr--next, .fotorama__arr--prev {
                display: none !important;
            }
        }
    }

    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .product-info-main {
            width: 57.565%;
            padding-left: 15px;
        }

        .product.media {
            width: 42.435%;
            padding-right: 15px;
        }
    }
}

.fotorama__thumb__arr--left .fotorama__thumb--icon {
    background: none;

    &:before {
        content: "\f104";
        font-family: @icons__font-awesome;
        font-weight: @icons__solid;
        display: inline-block;
        vertical-align: middle;
        font-size: 28px;
        color: #f89f08;
        text-align: center;
        transform: translateY(-50%);
        top: 50%;
        left: 10px;
        position: absolute;
    }
}

.fotorama__thumb__arr--right .fotorama__thumb--icon {
    background: none;

    &:before {
        content: "\f105";
        font-family: @icons__font-awesome;
        font-weight: @icons__solid;
        display: inline-block;
        vertical-align: middle;
        font-size: 28px;
        color: #f89f08;
        text-align: center;
        transform: translateY(-50%);
        top: 50%;
        right: 10px;
        position: absolute;
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Compare Products Page
    //  ---------------------------------------------

    body.catalog-product-compare-index {
        .action.print {
            float: right;
            margin: 15px 0;
        }
    }

    .table-wrapper.comparison {
        clear: both;
        max-width: 100%;
        overflow-x: auto;
    }

    .table-comparison {
        table-layout: fixed;

        .cell.label.remove,
        .cell.label.product {
            span {
                &:extend(.abs-visually-hidden all);
            }
        }

        .cell.label,
        td:last-child {
            border-right: @table__border-width @table__border-style @table__border-color;
        }

        .cell {
            padding: 15px;
            width: 140px;

            .attribute.value {
                overflow: hidden;
                width: 100%;
            }

            &.product.info,
            &.product.label {
                border-bottom: @table__border-width @table__border-style @table__border-color;
            }

            &.label {
                .attribute.label {
                    display: block;
                    width: 100%;
                    word-wrap: break-word;
                }
            }

            &.attribute {
                .lib-font-size(13);

                img {
                    height: auto;
                    max-width: 100%;
                }
            }
        }

        .product-item-photo {
            display: block;
            margin: 0 auto 15px;
        }

        .product-image-photo {
            margin-left: 0;
        }

        .product-item-actions,
        .price-box,
        .product.rating,
        .product-item-name {
            display: block;
            margin: 15px 0;
        }

        .product-addto-links {
            margin-top: 15px;

            .action.split,
            .action.toggle {
                .lib-button-s();
            }

            .action.toggle {
                padding: 0;
            }
        }

        .cell.remove {
            padding-bottom: 0;
            padding-top: 0;
            text-align: right;

            .action.delete {
                &:extend(.abs-remove-button-for-blocks all);
            }
        }

        .product-item-actions {
            > .actions-primary {
                + .actions-secondary {
                    margin-top: @indent__s;
                }
            }
        }

        .action {
            &.tocart {
                white-space: nowrap;
            }
        }
    }

    .comparison.headings {
        .lib-css(background, @page__background-color);
        left: 0;
        position: absolute;
        top: 0;
        width: auto;
        z-index: 2;
    }

    .block-compare {
        .block-title {
            &:extend(.abs-block-title all);
        }

        .product-item .product-item-name {
            margin-left: 22px;
        }

        .action {
            &.delete {
                &:extend(.abs-remove-button-for-blocks all);
                left: -6px;
                position: absolute;
                top: 0;
            }

            &.compare {
                &:extend(.abs-revert-secondary-color all);
            }
        }

        .counter {
            &:extend(.abs-block-items-counter all);
        }

        .actions-toolbar {
            margin: 17px 0 0;
        }
    }
}
