[class*='mgz-col-'] {
  width: 100%;
  float: left;
}
.mgz-col-xs-1, .mgz-col-sm-1, .mgz-col-md-1, .mgz-col-lg-1, .mgz-col-xs-2, .mgz-col-sm-2, .mgz-col-md-2, .mgz-col-lg-2, .mgz-col-xs-3, .mgz-col-sm-3, .mgz-col-md-3, .mgz-col-lg-3, .mgz-col-xs-4, .mgz-col-sm-4, .mgz-col-md-4, .mgz-col-lg-4, .mgz-col-xs-5, .mgz-col-sm-5, .mgz-col-md-5, .mgz-col-lg-5, .mgz-col-xs-6, .mgz-col-sm-6, .mgz-col-md-6, .mgz-col-lg-6, .mgz-col-xs-7, .mgz-col-sm-7, .mgz-col-md-7, .mgz-col-lg-7, .mgz-col-xs-8, .mgz-col-sm-8, .mgz-col-md-8, .mgz-col-lg-8, .mgz-col-xs-9, .mgz-col-sm-9, .mgz-col-md-9, .mgz-col-lg-9, .mgz-col-xs-10, .mgz-col-sm-10, .mgz-col-md-10, .mgz-col-lg-10, .mgz-col-xs-11, .mgz-col-sm-11, .mgz-col-md-11, .mgz-col-lg-11, .mgz-col-xs-12, .mgz-col-sm-12, .mgz-col-md-12, .mgz-col-lg-12, .mgz-col-xs-15, .mgz-col-sm-15, .mgz-col-md-15, .mgz-col-lg-15, .mgz-col-xs-25, .mgz-col-sm-25, .mgz-col-md-25, .mgz-col-lg-25, .mgz-col-xs-35, .mgz-col-sm-35, .mgz-col-md-35, .mgz-col-lg-35, .mgz-col-xs-45, .mgz-col-sm-45, .mgz-col-md-45, .mgz-col-lg-45 {
  position: relative;
  min-height: 1px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.mgz-col-xs-1, .mgz-col-xs-2, .mgz-col-xs-3, .mgz-col-xs-4, .mgz-col-xs-5, .mgz-col-xs-6, .mgz-col-xs-7, .mgz-col-xs-8, .mgz-col-xs-9, .mgz-col-xs-10, .mgz-col-xs-11, .mgz-col-xs-12, .mgz-col-xs-15, .mgz-col-xs-25, .mgz-col-xs-35, .mgz-col-xs-45 {
  float: left;
}
.mgz-col-xs-12 {
  width: 100%;
}
.mgz-col-xs-11 {
  width: 91.66666667%;
}
.mgz-col-xs-10 {
  width: 83.33333333%;
}
.mgz-col-xs-9 {
  width: 75%;
}
.mgz-col-xs-8 {
  width: 66.66666667%;
}
.mgz-col-xs-7 {
  width: 58.33333333%;
}
.mgz-col-xs-6 {
  width: 50%;
}
.mgz-col-xs-5 {
  width: 41.66666667%;
}
.mgz-col-xs-4 {
  width: 33.33333333%;
}
.mgz-col-xs-3 {
  width: 25%;
}
.mgz-col-xs-15 {
  width: 20%;
}
.mgz-col-xs-25 {
  width: 40%;
}
.mgz-col-xs-35 {
  width: 60%;
}
.mgz-col-xs-45 {
  width: 80%;
}
.mgz-col-xs-2 {
  width: 16.66666667%;
}
.mgz-col-xs-1 {
  width: 8.33333333%;
}
.mgz-col-xs-pull-12 {
  right: 100%;
}
.mgz-col-xs-pull-11 {
  right: 91.66666667%;
}
.mgz-col-xs-pull-10 {
  right: 83.33333333%;
}
.mgz-col-xs-pull-9 {
  right: 75%;
}
.mgz-col-xs-pull-8 {
  right: 66.66666667%;
}
.mgz-col-xs-pull-7 {
  right: 58.33333333%;
}
.mgz-col-xs-pull-6 {
  right: 50%;
}
.mgz-col-xs-pull-5 {
  right: 41.66666667%;
}
.mgz-col-xs-pull-4 {
  right: 33.33333333%;
}
.mgz-col-xs-pull-3 {
  right: 25%;
}
.mgz-col-xs-pull-15 {
  right: 20%;
}
.mgz-col-xs-pull-25 {
  right: 40%;
}
.mgz-col-xs-pull-35 {
  right: 60%;
}
.mgz-col-xs-pull-45 {
  right: 80%;
}
.mgz-col-xs-pull-2 {
  right: 16.66666667%;
}
.mgz-col-xs-pull-1 {
  right: 8.33333333%;
}
.mgz-col-xs-pull-0 {
  right: auto;
}
.mgz-col-xs-push-12 {
  left: 100%;
}
.mgz-col-xs-push-11 {
  left: 91.66666667%;
}
.mgz-col-xs-push-10 {
  left: 83.33333333%;
}
.mgz-col-xs-push-9 {
  left: 75%;
}
.mgz-col-xs-push-8 {
  left: 66.66666667%;
}
.mgz-col-xs-push-7 {
  left: 58.33333333%;
}
.mgz-col-xs-push-6 {
  left: 50%;
}
.mgz-col-xs-push-5 {
  left: 41.66666667%;
}
.mgz-col-xs-push-4 {
  left: 33.33333333%;
}
.mgz-col-xs-push-3 {
  left: 25%;
}
.mgz-col-xs-push-15 {
  left: 20%;
}
.mgz-col-xs-push-25 {
  left: 40%;
}
.mgz-col-xs-push-35 {
  left: 60%;
}
.mgz-col-xs-push-45 {
  left: 80%;
}
.mgz-col-xs-push-2 {
  left: 16.66666667%;
}
.mgz-col-xs-push-1 {
  left: 8.33333333%;
}
.mgz-col-xs-push-0 {
  left: auto;
}
.mgz-col-xs-offset-12 {
  margin-left: 100%;
}
.mgz-col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.mgz-col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.mgz-col-xs-offset-9 {
  margin-left: 75%;
}
.mgz-col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.mgz-col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.mgz-col-xs-offset-6 {
  margin-left: 50%;
}
.mgz-col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.mgz-col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.mgz-col-xs-offset-3 {
  margin-left: 25%;
}
.mgz-col-xs-offset-15 {
  margin-left: 20%;
}
.mgz-col-xs-offset-25 {
  margin-left: 40%;
}
.mgz-col-xs-offset-35 {
  margin-left: 60%;
}
.mgz-col-xs-offset-45 {
  margin-left: 80%;
}
.mgz-col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.mgz-col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.mgz-col-xs-offset-0 {
  margin-left: 0%;
}

.mgz-container {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
}

.mgz-row:before,
.mgz-row:after {
  content: " ";
  display: table;
}
.mgz-row:after {
  clear: both;
}

.mgz-text-left {
  text-align: left
}

.mgz-text-right {
  text-align: right
}

.mgz-text-center {
  text-align: center
}

.mgz-text-justify {
  text-align: justify
}

.f-right {
  float: right;
  width: auto;
}

.f-left {
  float: left;
  width: auto;
}

.f-none {
  float: none;
  width: auto;
}
@media (min-width: 576px) {
  .mgz-col-sm-1, .mgz-col-sm-2, .mgz-col-sm-3, .mgz-col-sm-4, .mgz-col-sm-5, .mgz-col-sm-6, .mgz-col-sm-7, .mgz-col-sm-8, .mgz-col-sm-9, .mgz-col-sm-10, .mgz-col-sm-11, .mgz-col-sm-12, .mgz-col-sm-15, .mgz-col-sm-25, .mgz-col-sm-35, .mgz-col-sm-45 {
    float: left;
  }
  .mgz-col-sm-12 {
    width: 100%;
  }
  .mgz-col-sm-11 {
    width: 91.66666667%;
  }
  .mgz-col-sm-10 {
    width: 83.33333333%;
  }
  .mgz-col-sm-9 {
    width: 75%;
  }
  .mgz-col-sm-8 {
    width: 66.66666667%;
  }
  .mgz-col-sm-7 {
    width: 58.33333333%;
  }
  .mgz-col-sm-6 {
    width: 50%;
  }
  .mgz-col-sm-5 {
    width: 41.66666667%;
  }
  .mgz-col-sm-4 {
    width: 33.33333333%;
  }
  .mgz-col-sm-3 {
    width: 25%;
  }
  .mgz-col-sm-15 {
    width: 20%;
  }
  .mgz-col-sm-25 {
    width: 40%;
  }
  .mgz-col-sm-35 {
    width: 60%;
  }
  .mgz-col-sm-45 {
    width: 80%;
  }
  .mgz-col-sm-2 {
    width: 16.66666667%;
  }
  .mgz-col-sm-1 {
    width: 8.33333333%;
  }
  .mgz-col-sm-pull-12 {
    right: 100%;
  }
  .mgz-col-sm-pull-11 {
    right: 91.66666667%;
  }
  .mgz-col-sm-pull-10 {
    right: 83.33333333%;
  }
  .mgz-col-sm-pull-9 {
    right: 75%;
  }
  .mgz-col-sm-pull-8 {
    right: 66.66666667%;
  }
  .mgz-col-sm-pull-7 {
    right: 58.33333333%;
  }
  .mgz-col-sm-pull-6 {
    right: 50%;
  }
  .mgz-col-sm-pull-5 {
    right: 41.66666667%;
  }
  .mgz-col-sm-pull-4 {
    right: 33.33333333%;
  }
  .mgz-col-sm-pull-3 {
    right: 25%;
  }
  .mgz-col-sm-pull-15 {
    right: 20%;
  }
  .mgz-col-sm-pull-25 {
    right: 40%;
  }
  .mgz-col-sm-pull-35 {
    right: 60%;
  }
  .mgz-col-sm-pull-45 {
    right: 80%;
  }
  .mgz-col-sm-pull-2 {
    right: 16.66666667%;
  }
  .mgz-col-sm-pull-1 {
    right: 8.33333333%;
  }
  .mgz-col-sm-pull-0 {
    right: auto;
  }
  .mgz-col-sm-push-12 {
    left: 100%;
  }
  .mgz-col-sm-push-11 {
    left: 91.66666667%;
  }
  .mgz-col-sm-push-10 {
    left: 83.33333333%;
  }
  .mgz-col-sm-push-9 {
    left: 75%;
  }
  .mgz-col-sm-push-8 {
    left: 66.66666667%;
  }
  .mgz-col-sm-push-7 {
    left: 58.33333333%;
  }
  .mgz-col-sm-push-6 {
    left: 50%;
  }
  .mgz-col-sm-push-5 {
    left: 41.66666667%;
  }
  .mgz-col-sm-push-4 {
    left: 33.33333333%;
  }
  .mgz-col-sm-push-3 {
    left: 25%;
  }
  .mgz-col-sm-push-15 {
    left: 20%;
  }
  .mgz-col-sm-push-25 {
    left: 40%;
  }
  .mgz-col-sm-push-35 {
    left: 60%;
  }
  .mgz-col-sm-push-45 {
    left: 80%;
  }
  .mgz-col-sm-push-2 {
    left: 16.66666667%;
  }
  .mgz-col-sm-push-1 {
    left: 8.33333333%;
  }
  .mgz-col-sm-push-0 {
    left: auto;
  }
  .mgz-col-sm-offset-12 {
    margin-left: 100%;
  }
  .mgz-col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .mgz-col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .mgz-col-sm-offset-9 {
    margin-left: 75%;
  }
  .mgz-col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .mgz-col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .mgz-col-sm-offset-6 {
    margin-left: 50%;
  }
  .mgz-col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .mgz-col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .mgz-col-sm-offset-3 {
    margin-left: 25%;
  }
  .mgz-col-sm-offset-15 {
    margin-left: 20%;
  }
  .mgz-col-sm-offset-25 {
    margin-left: 40%;
  }
  .mgz-col-sm-offset-35 {
    margin-left: 60%;
  }
  .mgz-col-sm-offset-45 {
    margin-left: 80%;
  }
  .mgz-col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .mgz-col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .mgz-col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 768px) {
  .mgz-col-md-1, .mgz-col-md-2, .mgz-col-md-3, .mgz-col-md-4, .mgz-col-md-5, .mgz-col-md-6, .mgz-col-md-7, .mgz-col-md-8, .mgz-col-md-9, .mgz-col-md-10, .mgz-col-md-11, .mgz-col-md-12, .mgz-col-md-15, .mgz-col-md-25, .mgz-col-md-35, .mgz-col-md-45 {
    float: left;
  }
  .mgz-col-md-12 {
    width: 100%;
  }
  .mgz-col-md-11 {
    width: 91.66666667%;
  }
  .mgz-col-md-10 {
    width: 83.33333333%;
  }
  .mgz-col-md-9 {
    width: 75%;
  }
  .mgz-col-md-8 {
    width: 66.66666667%;
  }
  .mgz-col-md-7 {
    width: 58.33333333%;
  }
  .mgz-col-md-6 {
    width: 50%;
  }
  .mgz-col-md-5 {
    width: 41.66666667%;
  }
  .mgz-col-md-4 {
    width: 33.33333333%;
  }
  .mgz-col-md-3 {
    width: 25%;
  }
  .mgz-col-md-15 {
    width: 20%;
  }
  .mgz-col-md-25 {
    width: 40%;
  }
  .mgz-col-md-35 {
    width: 60%;
  }
  .mgz-col-md-45 {
    width: 80%;
  }
  .mgz-col-md-2 {
    width: 16.66666667%;
  }
  .mgz-col-md-1 {
    width: 8.33333333%;
  }
  .mgz-col-md-pull-12 {
    right: 100%;
  }
  .mgz-col-md-pull-11 {
    right: 91.66666667%;
  }
  .mgz-col-md-pull-10 {
    right: 83.33333333%;
  }
  .mgz-col-md-pull-9 {
    right: 75%;
  }
  .mgz-col-md-pull-8 {
    right: 66.66666667%;
  }
  .mgz-col-md-pull-7 {
    right: 58.33333333%;
  }
  .mgz-col-md-pull-6 {
    right: 50%;
  }
  .mgz-col-md-pull-5 {
    right: 41.66666667%;
  }
  .mgz-col-md-pull-4 {
    right: 33.33333333%;
  }
  .mgz-col-md-pull-3 {
    right: 25%;
  }
  .mgz-col-md-pull-15 {
    right: 20%;
  }
  .mgz-col-md-pull-25 {
    right: 40%;
  }
  .mgz-col-md-pull-35 {
    right: 60%;
  }
  .mgz-col-md-pull-45 {
    right: 80%;
  }
  .mgz-col-md-pull-2 {
    right: 16.66666667%;
  }
  .mgz-col-md-pull-1 {
    right: 8.33333333%;
  }
  .mgz-col-md-pull-0 {
    right: auto;
  }
  .mgz-col-md-push-12 {
    left: 100%;
  }
  .mgz-col-md-push-11 {
    left: 91.66666667%;
  }
  .mgz-col-md-push-10 {
    left: 83.33333333%;
  }
  .mgz-col-md-push-9 {
    left: 75%;
  }
  .mgz-col-md-push-8 {
    left: 66.66666667%;
  }
  .mgz-col-md-push-7 {
    left: 58.33333333%;
  }
  .mgz-col-md-push-6 {
    left: 50%;
  }
  .mgz-col-md-push-5 {
    left: 41.66666667%;
  }
  .mgz-col-md-push-4 {
    left: 33.33333333%;
  }
  .mgz-col-md-push-3 {
    left: 25%;
  }
  .mgz-col-md-push-15 {
    left: 20%;
  }
  .mgz-col-md-push-25 {
    left: 40%;
  }
  .mgz-col-md-push-35 {
    left: 60%;
  }
  .mgz-col-md-push-45 {
    left: 80%;
  }
  .mgz-col-md-push-2 {
    left: 16.66666667%;
  }
  .mgz-col-md-push-1 {
    left: 8.33333333%;
  }
  .mgz-col-md-push-0 {
    left: auto;
  }
  .mgz-col-md-offset-12 {
    margin-left: 100%;
  }
  .mgz-col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .mgz-col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .mgz-col-md-offset-9 {
    margin-left: 75%;
  }
  .mgz-col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .mgz-col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .mgz-col-md-offset-6 {
    margin-left: 50%;
  }
  .mgz-col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .mgz-col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .mgz-col-md-offset-3 {
    margin-left: 25%;
  }
  .mgz-col-md-offset-15 {
    margin-left: 20%;
  }
  .mgz-col-md-offset-25 {
    margin-left: 40%;
  }
  .mgz-col-md-offset-35 {
    margin-left: 60%;
  }
  .mgz-col-md-offset-45 {
    margin-left: 80%;
  }
  .mgz-col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .mgz-col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .mgz-col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .mgz-col-lg-1, .mgz-col-lg-2, .mgz-col-lg-3, .mgz-col-lg-4, .mgz-col-lg-5, .mgz-col-lg-6, .mgz-col-lg-7, .mgz-col-lg-8, .mgz-col-lg-9, .mgz-col-lg-10, .mgz-col-lg-11, .mgz-col-lg-12, .mgz-col-lg-15, .mgz-col-lg-25, .mgz-col-lg-35, .mgz-col-lg-45 {
    float: left;
  }
  .mgz-col-lg-12 {
    width: 100%;
  }
  .mgz-col-lg-11 {
    width: 91.66666667%;
  }
  .mgz-col-lg-10 {
    width: 83.33333333%;
  }
  .mgz-col-lg-9 {
    width: 75%;
  }
  .mgz-col-lg-8 {
    width: 66.66666667%;
  }
  .mgz-col-lg-7 {
    width: 58.33333333%;
  }
  .mgz-col-lg-6 {
    width: 50%;
  }
  .mgz-col-lg-5 {
    width: 41.66666667%;
  }
  .mgz-col-lg-4 {
    width: 33.33333333%;
  }
  .mgz-col-lg-3 {
    width: 25%;
  }
  .mgz-col-lg-15 {
    width: 20%;
  }
  .mgz-col-lg-25 {
    width: 40%;
  }
  .mgz-col-lg-35 {
    width: 60%;
  }
  .mgz-col-lg-45 {
    width: 80%;
  }
  .mgz-col-lg-2 {
    width: 16.66666667%;
  }
  .mgz-col-lg-1 {
    width: 8.33333333%;
  }
  .mgz-col-lg-pull-12 {
    right: 100%;
  }
  .mgz-col-lg-pull-11 {
    right: 91.66666667%;
  }
  .mgz-col-lg-pull-10 {
    right: 83.33333333%;
  }
  .mgz-col-lg-pull-9 {
    right: 75%;
  }
  .mgz-col-lg-pull-8 {
    right: 66.66666667%;
  }
  .mgz-col-lg-pull-7 {
    right: 58.33333333%;
  }
  .mgz-col-lg-pull-6 {
    right: 50%;
  }
  .mgz-col-lg-pull-5 {
    right: 41.66666667%;
  }
  .mgz-col-lg-pull-4 {
    right: 33.33333333%;
  }
  .mgz-col-lg-pull-3 {
    right: 25%;
  }
  .mgz-col-lg-pull-15 {
    right: 20%;
  }
  .mgz-col-lg-pull-25 {
    right: 40%;
  }
  .mgz-col-lg-pull-35 {
    right: 60%;
  }
  .mgz-col-lg-pull-45 {
    right: 80%;
  }
  .mgz-col-lg-pull-2 {
    right: 16.66666667%;
  }
  .mgz-col-lg-pull-1 {
    right: 8.33333333%;
  }
  .mgz-col-lg-pull-0 {
    right: auto;
  }
  .mgz-col-lg-push-12 {
    left: 100%;
  }
  .mgz-col-lg-push-11 {
    left: 91.66666667%;
  }
  .mgz-col-lg-push-10 {
    left: 83.33333333%;
  }
  .mgz-col-lg-push-9 {
    left: 75%;
  }
  .mgz-col-lg-push-8 {
    left: 66.66666667%;
  }
  .mgz-col-lg-push-7 {
    left: 58.33333333%;
  }
  .mgz-col-lg-push-6 {
    left: 50%;
  }
  .mgz-col-lg-push-5 {
    left: 41.66666667%;
  }
  .mgz-col-lg-push-4 {
    left: 33.33333333%;
  }
  .mgz-col-lg-push-3 {
    left: 25%;
  }
  .mgz-col-lg-push-15 {
    left: 20%;
  }
  .mgz-col-lg-push-25 {
    left: 40%;
  }
  .mgz-col-lg-push-35 {
    left: 60%;
  }
  .mgz-col-lg-push-45 {
    left: 80%;
  }
  .mgz-col-lg-push-2 {
    left: 16.66666667%;
  }
  .mgz-col-lg-push-1 {
    left: 8.33333333%;
  }
  .mgz-col-lg-push-0 {
    left: auto;
  }
  .mgz-col-lg-offset-12 {
    margin-left: 100%;
  }
  .mgz-col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .mgz-col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .mgz-col-lg-offset-9 {
    margin-left: 75%;
  }
  .mgz-col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .mgz-col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .mgz-col-lg-offset-6 {
    margin-left: 50%;
  }
  .mgz-col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .mgz-col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .mgz-col-lg-offset-3 {
    margin-left: 25%;
  }
  .mgz-col-lg-offset-15 {
    margin-left: 20%;
  }
  .mgz-col-lg-offset-25 {
    margin-left: 40%;
  }
  .mgz-col-lg-offset-35 {
    margin-left: 60%;
  }
  .mgz-col-lg-offset-45 {
    margin-left: 80%;
  }
  .mgz-col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .mgz-col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .mgz-col-lg-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 1200px) {
  .mgz-col-xl-1, .mgz-col-xl-2, .mgz-col-xl-3, .mgz-col-xl-4, .mgz-col-xl-5, .mgz-col-xl-6, .mgz-col-xl-7, .mgz-col-xl-8, .mgz-col-xl-9, .mgz-col-xl-10, .mgz-col-xl-11, .mgz-col-xl-12, .mgz-col-xl-15, .mgz-col-xl-25, .mgz-col-xl-35, .mgz-col-xl-45 {
    float: left;
  }
  .mgz-col-xl-12 {
    width: 100%;
  }
  .mgz-col-xl-11 {
    width: 91.66666667%;
  }
  .mgz-col-xl-10 {
    width: 83.33333333%;
  }
  .mgz-col-xl-9 {
    width: 75%;
  }
  .mgz-col-xl-8 {
    width: 66.66666667%;
  }
  .mgz-col-xl-7 {
    width: 58.33333333%;
  }
  .mgz-col-xl-6 {
    width: 50%;
  }
  .mgz-col-xl-5 {
    width: 41.66666667%;
  }
  .mgz-col-xl-4 {
    width: 33.33333333%;
  }
  .mgz-col-xl-3 {
    width: 25%;
  }
  .mgz-col-xl-15 {
    width: 20%;
  }
  .mgz-col-xl-25 {
    width: 40%;
  }
  .mgz-col-xl-35 {
    width: 60%;
  }
  .mgz-col-xl-45 {
    width: 80%;
  }
  .mgz-col-xl-2 {
    width: 16.66666667%;
  }
  .mgz-col-xl-1 {
    width: 8.33333333%;
  }
  .mgz-col-xl-pull-12 {
    right: 100%;
  }
  .mgz-col-xl-pull-11 {
    right: 91.66666667%;
  }
  .mgz-col-xl-pull-10 {
    right: 83.33333333%;
  }
  .mgz-col-xl-pull-9 {
    right: 75%;
  }
  .mgz-col-xl-pull-8 {
    right: 66.66666667%;
  }
  .mgz-col-xl-pull-7 {
    right: 58.33333333%;
  }
  .mgz-col-xl-pull-6 {
    right: 50%;
  }
  .mgz-col-xl-pull-5 {
    right: 41.66666667%;
  }
  .mgz-col-xl-pull-4 {
    right: 33.33333333%;
  }
  .mgz-col-xl-pull-3 {
    right: 25%;
  }
  .mgz-col-xl-pull-15 {
    right: 20%;
  }
  .mgz-col-xl-pull-25 {
    right: 40%;
  }
  .mgz-col-xl-pull-35 {
    right: 60%;
  }
  .mgz-col-xl-pull-45 {
    right: 80%;
  }
  .mgz-col-xl-pull-2 {
    right: 16.66666667%;
  }
  .mgz-col-xl-pull-1 {
    right: 8.33333333%;
  }
  .mgz-col-xl-pull-0 {
    right: auto;
  }
  .mgz-col-xl-push-12 {
    left: 100%;
  }
  .mgz-col-xl-push-11 {
    left: 91.66666667%;
  }
  .mgz-col-xl-push-10 {
    left: 83.33333333%;
  }
  .mgz-col-xl-push-9 {
    left: 75%;
  }
  .mgz-col-xl-push-8 {
    left: 66.66666667%;
  }
  .mgz-col-xl-push-7 {
    left: 58.33333333%;
  }
  .mgz-col-xl-push-6 {
    left: 50%;
  }
  .mgz-col-xl-push-5 {
    left: 41.66666667%;
  }
  .mgz-col-xl-push-4 {
    left: 33.33333333%;
  }
  .mgz-col-xl-push-3 {
    left: 25%;
  }
  .mgz-col-xl-push-15 {
    left: 20%;
  }
  .mgz-col-xl-push-25 {
    left: 40%;
  }
  .mgz-col-xl-push-35 {
    left: 60%;
  }
  .mgz-col-xl-push-45 {
    left: 80%;
  }
  .mgz-col-xl-push-2 {
    left: 16.66666667%;
  }
  .mgz-col-xl-push-1 {
    left: 8.33333333%;
  }
  .mgz-col-xl-push-0 {
    left: auto;
  }
  .mgz-col-xl-offset-12 {
    margin-left: 100%;
  }
  .mgz-col-xl-offset-11 {
    margin-left: 91.66666667%;
  }
  .mgz-col-xl-offset-10 {
    margin-left: 83.33333333%;
  }
  .mgz-col-xl-offset-9 {
    margin-left: 75%;
  }
  .mgz-col-xl-offset-8 {
    margin-left: 66.66666667%;
  }
  .mgz-col-xl-offset-7 {
    margin-left: 58.33333333%;
  }
  .mgz-col-xl-offset-6 {
    margin-left: 50%;
  }
  .mgz-col-xl-offset-5 {
    margin-left: 41.66666667%;
  }
  .mgz-col-xl-offset-4 {
    margin-left: 33.33333333%;
  }
  .mgz-col-xl-offset-3 {
    margin-left: 25%;
  }
  .mgz-col-xl-offset-15 {
    margin-left: 20%;
  }
  .mgz-col-xl-offset-25 {
    margin-left: 40%;
  }
  .mgz-col-xl-offset-35 {
    margin-left: 60%;
  }
  .mgz-col-xl-offset-45 {
    margin-left: 80%;
  }
  .mgz-col-xl-offset-2 {
    margin-left: 16.66666667%;
  }
  .mgz-col-xl-offset-1 {
    margin-left: 8.33333333%;
  }
  .mgz-col-xl-offset-0 {
    margin-left: 0%;
  }
}
@media (max-width: 575px) {
  .mgz-grid-col-xs-6 > div:nth-child(6n+1),
  .mgz-grid-col-xs-3 > div:nth-child(3n+1),
  .mgz-grid-col-xs-4 > div:nth-child(4n+1),
  .mgz-grid-col-xs-15 > div:nth-child(5n+1),
  .mgz-grid-col-xs-2 > div:nth-child(2n+1) {
    clear: left;
  }
  .mgz-grid-col-xs-6> div,
  .mgz-grid-col-xs-3 > div,
  .mgz-grid-col-xs-4 > div,
  .mgz-grid-col-xs-15 > div,
  .mgz-grid-col-xs-2 > div {
    float: left;
  }
  .mgz-grid-col-xs-6 > div {
    width: 16.66666667%;
  }
  .mgz-grid-col-xs-3 > div {
    width: 33.33333333%;
  }
  .mgz-grid-col-xs-4 > div {
    width: 25%;
  }
  .mgz-grid-col-xs-15 > div {
    width: 20%;
  }
  .mgz-grid-col-xs-2 > div {
    width: 50%;
  }
  .mgz-hidden-xs {
    display: none !important;
  }
  .xs_left {
    text-align: left;
  }
  .xs_center {
    text-align: center;
  }
  .xs_right {
    text-align: right;
  }
  .xs_f-right {
    float: right;
  }
  .xs_f-left {
    float: left;
  }
  .xs_f-none {
    float: none;
  }
}
@media (min-width: 576px) and (max-width: 767px) {
  .mgz-grid-col-xs-12 > div:nth-child(12n+1),
  .mgz-grid-col-xs-6 > div:nth-child(6n+1),
  .mgz-grid-col-xs-5 > div:nth-child(5n+1),
  .mgz-grid-col-xs-4 > div:nth-child(4n+1),
  .mgz-grid-col-xs-3 > div:nth-child(3n+1),
  .mgz-grid-col-xs-2 > div:nth-child(2n+1),
  .mgz-grid-col-xs-1 > div:nth-child(1n+1) {
    clear: left;
  }
  .mgz-grid-col-xs-12 > div,
  .mgz-grid-col-xs-6 > div,
  .mgz-grid-col-xs-5 > div,
  .mgz-grid-col-xs-4 > div,
  .mgz-grid-col-xs-3 > div,
  .mgz-grid-col-xs-2 > div,
  .mgz-grid-col-xs-1 > div {
    float: left;
  }
  .mgz-grid-col-xs-12 > div {
    width: 8.33333333%;
  }
  .mgz-grid-col-xs-6 > div {
    width: 16.66666667%;
  }
  .mgz-grid-col-xs-5 > div {
    width: 20%;
  }
  .mgz-grid-col-xs-4 > div {
    width: 25%;
  }
  .mgz-grid-col-xs-3 > div {
    width: 33.33333333%;
  }
  .mgz-grid-col-xs-2 > div {
    width: 50%;
  }
  .mgz-grid-col-xs-1 > div {
    width: 100%;
  }
  .mgz-hidden-sm {
    display: none !important;
  }
  .sm_left {
    text-align: left;
  }
  .sm_center {
    text-align: center;
  }
  .sm_right {
    text-align: right;
  }
  .sm_f-right {
    float: right;
  }
  .sm_f-left {
    float: left;
  }
  .sm_f-none {
    float: none;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mgz-grid-col-md-12 > div:nth-child(12n+1),
  .mgz-grid-col-md-6 > div:nth-child(6n+1),
  .mgz-grid-col-md-5 > div:nth-child(5n+1),
  .mgz-grid-col-md-4 > div:nth-child(4n+1),
  .mgz-grid-col-md-3 > div:nth-child(3n+1),
  .mgz-grid-col-md-2 > div:nth-child(2n+1),
  .mgz-grid-col-md-1 > div:nth-child(1n+1) {
    clear: left;
  }
  .mgz-grid-col-md-12 > div,
  .mgz-grid-col-md-6 > div,
  .mgz-grid-col-md-5 > div,
  .mgz-grid-col-md-4 > div,
  .mgz-grid-col-md-3 > div,
  .mgz-grid-col-md-2 > div,
  .mgz-grid-col-md-1 > div {
    float: left;
  }
  .mgz-grid-col-md-12 > div {
    width: 8.33333333%;
  }
  .mgz-grid-col-md-6 > div {
    width: 16.66666667%;
  }
  .mgz-grid-col-md-5 > div {
    width: 20%;
  }
  .mgz-grid-col-md-4 > div {
    width: 25%;
  }
  .mgz-grid-col-md-3 > div {
    width: 33.33333333%;
  }
  .mgz-grid-col-md-2 > div {
    width: 50%;
  }
  .mgz-grid-col-md-1 > div {
    width: 100%;
  }
  .mgz-hidden-md {
    display: none !important;
  }
  .md_left {
    text-align: left;
  }
  .md_center {
    text-align: center;
  }
  .md_right {
    text-align: right;
  }
  .md_f-right {
    float: right;
  }
  .md_f-left {
    float: left;
  }
  .md_f-none {
    float: none;
  }
}
@media (min-width: 992px) and (max-width: 1200px) {
  .mgz-grid-col-lg-12 > div:nth-child(12n+1),
  .mgz-grid-col-lg-6 > div:nth-child(6n+1),
  .mgz-grid-col-lg-5 > div:nth-child(5n+1),
  .mgz-grid-col-lg-4 > div:nth-child(4n+1),
  .mgz-grid-col-lg-3 > div:nth-child(3n+1),
  .mgz-grid-col-lg-2 > div:nth-child(2n+1),
  .mgz-grid-col-lg-1 > div:nth-child(1n+1) {
    clear: left;
  }
  .mgz-grid-col-lg-12 > div,
  .mgz-grid-col-lg-6 > div,
  .mgz-grid-col-lg-5 > div,
  .mgz-grid-col-lg-4 > div,
  .mgz-grid-col-lg-3 > div,
  .mgz-grid-col-lg-2 > div,
  .mgz-grid-col-lg-1 > div {
    float: left;
  }
  .mgz-grid-col-lg-12 > div {
    width: 8.33333333%;
  }
  .mgz-grid-col-lg-6 > div {
    width: 16.66666667%;
  }
  .mgz-grid-col-lg-5 > div {
    width: 20%;
  }
  .mgz-grid-col-lg-4 > div {
    width: 25%;
  }
  .mgz-grid-col-lg-3 > div {
    width: 33.33333333%;
  }
  .mgz-grid-col-lg-2 > div {
    width: 50%;
  }
  .mgz-grid-col-lg-1 > div {
    width: 100%;
  }
  .mgz-hidden-lg {
    display: none !important;
  }
  .lg_left {
    text-align: left;
  }
  .lg_center {
    text-align: center;
  }
  .lg_right {
    text-align: right;
  }
  .lg_f-right {
    float: right;
  }
  .lg_f-left {
    float: left;
  }
  .lg_f-none {
    float: none;
  }
}
@media (min-width: 1200px) {
  .mgz-grid-col-xl-12 > div:nth-child(12n+1),
  .mgz-grid-col-xl-6 > div:nth-child(6n+1),
  .mgz-grid-col-xl-5 > div:nth-child(5n+1),
  .mgz-grid-col-xl-4 > div:nth-child(4n+1),
  .mgz-grid-col-xl-3 > div:nth-child(3n+1),
  .mgz-grid-col-xl-2 > div:nth-child(2n+1),
  .mgz-grid-col-xl-1 > div:nth-child(1n+1) {
    clear: left;
  }
  .mgz-grid-col-xl-12 > div,
  .mgz-grid-col-xl-6 > div,
  .mgz-grid-col-xl-5 > div,
  .mgz-grid-col-xl-4 > div,
  .mgz-grid-col-xl-3 > div,
  .mgz-grid-col-xl-2 > div,
  .mgz-grid-col-xl-1 > div {
    float: left;
  }
  .mgz-grid-col-xl-12 > div {
    width: 8.33333333%;
  }
  .mgz-grid-col-xl-6 > div {
    width: 16.66666667%;
  }
  .mgz-grid-col-xl-5 > div {
    width: 20%;
  }
  .mgz-grid-col-xl-4 > div {
    width: 25%;
  }
  .mgz-grid-col-xl-3 > div {
    width: 33.33333333%;
  }
  .mgz-grid-col-xl-2 > div {
    width: 50%;
  }
  .mgz-grid-col-xl-1 > div {
    width: 100%;
  }
  .mgz-hidden-xl {
    display: none !important;
  }
  .xl_left {
    text-align: left;
  }
  .xl_center {
    text-align: center;
  }
  .xl_right {
    text-align: right;
  }
  .xl_f-right {
    float: right;
  }
  .xl_f-left {
    float: left;
  }
  .xl_f-none {
    float: none;
  }
}
.mgz-pull-right {
  float: right !important;
}
.mgz-pull-left {
  float: left !important;
}