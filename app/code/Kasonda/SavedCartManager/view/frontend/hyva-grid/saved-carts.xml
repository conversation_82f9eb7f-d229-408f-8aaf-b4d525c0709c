<?xml version="1.0"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Hyva_Admin:etc/hyva-grid.xsd">
    <source>
        <collection>Magento\Quote\Model\ResourceModel\Quote\Collection</collection>
        <defaultSearchCriteriaBindings>
            <field name="customer_id" condition="eq" method="Magento\Customer\Model\Session::getCustomerId" param="customer_id"/>
            <field name="saved_quote" condition="eq" value="1"/>
        </defaultSearchCriteriaBindings>
    </source>
    <columns rowAction="view">
        <include>
            <column name="is_active" label="Aktiv" sortable="false"/>
            <column name="updated_at" label="Datum" type="dateonly" sortable="false"/>
            <column name="commission" label="Kommission" sortable="false"/>
            <column name="commission_notes" label="Notizen" sortable="false"/>
            <column name="grand_total" label="Summe" type="price" sortable="false"/>
            <column name="entity_id" label="Summe" type="hvkprice" sortable="false"/>
            <column name="is_ordered" label="Status" type="cartstatus" sortable="false"/>
        </include>
    </columns>
    <actions idColumn="entity_id">
        <action id="load" label="laden" url="savedcartmanager/cart/load" idParam="cart_id" >
            <event on="click"></event>
        </action>
        <action id="delete" label="löschen" url="savedcartmanger/cart/overview" idParam="cart_id" >
            <event on="click"></event>
        </action>
        <action id="view" label=" " url="savedcartmanager/cart/view" idParam="cart_id">
            <event on="click"></event>
        </action>
    </actions>
    <entityConfig>
        <label>
            <singular>Cart</singular>
            <plural>Carts</plural>
        </label>
    </entityConfig>
    <navigation>
        <sorting>
            <defaultSortByColumn>updated_at</defaultSortByColumn>
            <defaultSortDirection>asc</defaultSortDirection>
        </sorting>
        <pager>
            <defaultPageSize>100</defaultPageSize>
            <pageSizes>25, 50, 100</pageSizes>
        </pager>
    </navigation>
</grid>
