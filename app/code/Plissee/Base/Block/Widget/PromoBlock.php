<?php
/**
 * PromoBlock
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace Plissee\Base\Block\Widget;

use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Encryption\Encryptor;
use MagePal\GoogleTagManager\Block\DataLayer;

class PromoBlock extends AbstractWidget
{

    /**
     * Path to template file in theme.
     * @var string
     */
    protected $_template = 'Plissee_Base::widget/promobox.phtml';
    protected $_productRepository;
    protected $_encryptor;
    /**
     * @var \Magento\Cms\Helper\Page
     */
    private $_cmsPage;
    /**
     * @var \Plissee\Base\Helper\Configurator
     */
    private $configuratorHelper;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Cms\Model\Template\FilterProvider $filterProvider,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\Image\Factory $imageFactory,
        \Magento\Framework\Serialize\Serializer\Json $serializer,
        ProductRepository $productRepository,
        Encryptor $encryptor,
        \Magento\Cms\Helper\Page $cmsPage,
        \Plissee\Base\Helper\Configurator $configuratorHelper,
        array $data = []
    ) {
        parent::__construct($context, $filterProvider, $filesystem, $imageFactory, $serializer, $data);
        $this->_productRepository = $productRepository;
        $this->_encryptor = $encryptor;
        $this->_cmsPage = $cmsPage;
        $this->configuratorHelper = $configuratorHelper;

    }

    public function _prepareLayout()
    {
    }

    /**
     * Render block HTML
     * @return string
     */
    protected function _toHtml()
    {
        if ($data = $this->getData()) {
            foreach ($data as $field => $value) {
                if (preg_match('/^(content)_(.*)/i', $field, $matches)) {
                    if ($value) {
                        $value = $this->_base64Decode($value);
                        $this->setData($matches[2], $value);
                        $this->unsetData($matches[0]);
                    }
                }
            }
            if (!$this->getData('button_label')) {
                $this->setData('button_label', __('Read More'));
            }
            $this->setData('content', $this->_base64Decode($this->getData('content')));
            $this->setData('button_url', $this->_getButtonUrl());
        }

        //provide dynamic name by appending hashed suffix to block name
        $time = microtime(false);
        $suffix = $this->_encryptor->getHash($time);

        //tracking
        /** @var $tm DataLayer */
        $tm = $this->getLayout()->createBlock(DataLayer::class, 'promoViewTracking-promoBlock-' . $suffix);

        if ($tm && $this->getData("tracking_id") && $this->getData("tracking_name")) {
            $tm->setTemplate('MagePal_GoogleTagManager::js.phtml');
            $obj = (object)[
                'id'   => $this->getData("tracking_id"),
                'name' => $this->getData("tracking_name"),
            ];

            if ($this->getData("tracking_creative")) {
                $obj->creative = $this->getData("tracking_creative");
            }
            if ($this->getData("tracking_position")) {
                $obj->position = $this->getData("tracking_position");
            }

            $promotions = [$obj];

            $dataLayer = [
                'event'     => 'promoView',
                'ecommerce' =>
                    ['promoView' => ['promotions' => $promotions]],
            ];
            $tm->addCustomDataLayerByEvent('promoView', $dataLayer);
        }
        $html = $tm->toHtml();

        return parent::_toHtml() . $html;
    }

    protected function _getButtonUrl()
    {
        if ($this->getData("direct_link")) {
            $productId = $this->getData("direct_link");
            $productId = trim($productId, "product/");
            $product = $this->_productRepository->getById($productId);

            return $product->getProductUrl();
        } elseif ($this->getData("configurator_link")) {
            $productId = $this->getData("configurator_link");
            $productId = trim($productId, "product/");
            $product = $this->_productRepository->getById($productId);
            return $this->configuratorHelper->getConfiguratorLink($product);

        } elseif ($this->getData("button_url")) {
            return $this->_cmsPage->getPageUrl($this->getData('button_url'));
        }
    }
}
