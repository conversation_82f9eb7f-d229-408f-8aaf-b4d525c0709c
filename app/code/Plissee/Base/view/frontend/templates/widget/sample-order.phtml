<?php
/** @var $block \Plissee\Base\Block\Widget\SampleOrder */
?>
<base href="<?php echo $block->getSampleOrderBaseUrl(); ?>"
      data-mage-init='{"configurator-frame": {"params": <?= json_encode($block->getParams());?>}}'/>

<?php if($cssIncludes = $this->getData('css-includes')): ?>
    <?php foreach($cssIncludes as $cssInclude): ?>
        <?= $cssInclude ?>
    <?php endforeach; ?>
<?php endif; ?>

<div id="configurator-root" class="ve-configurator"<?php echo $block->getParamsForDataAttribute();?>></div>

<?php if($jsIncludes = $this->getData('js-includes')): ?>
    <?php foreach($jsIncludes as $jsInclude): ?>
        <?= $jsInclude ?>
    <?php endforeach; ?>
<?php endif; ?>
