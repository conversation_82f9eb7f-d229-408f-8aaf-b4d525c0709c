<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Plugin;

use CopeX\SerialNumber\Api\Data\SerialNumberInterface;
use CopeX\SerialNumber\Controller\Adminhtml\SerialNumber\Import as ImportModel;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Type;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ProductRepository;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Eav\Api\Data\AttributeOptionLabelInterfaceFactory;
use Magento\Framework\Message\ManagerInterface;
use Vivo\SerialNumber\Helper\Data;

class Import
{
    private ProductFactory $productFactory;
    private ProductRepository $productRepository;
    private AttributeRepositoryInterface $attributeRepository;
    private AttributeOptionManagementInterface $attributeOptionManagement;
    private AttributeOptionLabelInterfaceFactory $optionLabelFactory;
    private AttributeOptionInterfaceFactory $optionFactory;
    private ManagerInterface $messageManager;
    private Data $helper;

    public function __construct(
        ProductFactory $productFactory,
        ProductRepository $productRepository,
        AttributeRepositoryInterface $attributeRepository,
        AttributeOptionManagementInterface $attributeOptionManagement,
        AttributeOptionLabelInterfaceFactory $optionLabelFactory,
        AttributeOptionInterfaceFactory $optionFactory,
        ManagerInterface $messageManager,
        Data $helper
    ) {
        $this->productFactory = $productFactory;
        $this->productRepository = $productRepository;
        $this->attributeRepository = $attributeRepository;
        $this->attributeOptionManagement = $attributeOptionManagement;
        $this->optionLabelFactory = $optionLabelFactory;
        $this->optionFactory = $optionFactory;
        $this->messageManager = $messageManager;
        $this->helper = $helper;
    }

    public function afterGetMappedData(ImportModel $subject, $result): mixed
    {
        if ($this->hasSupplierName($result)) {
            if (is_int($result['start_date'])) {
                $result['start_date'] = $this->helper->excelDateToDate($result['start_date']);
            }
            if (is_int($result['end_date'])) {
                $result['end_date'] = $this->helper->excelDateToDate($result['end_date']);
            }
            if ($this->helper->checkDate($result['start_date']) && $this->helper->checkDate($result['end_date'])) {
                $result = $this->processMappedData($result);
            } else {
                $this->messageManager->addErrorMessage(__('Start or End Date is incorrect. Voucher Number: %1', $result['voucher_code']));
            }
        }

        return $result;
    }

    public function afterGetRequiredColumns(ImportModel $subject, $result)
    {
        return ['LieferantName', 'VoucherValue', 'VoucherCode', 'VoucherLink', 'StartDate', 'EndDate'];
    }

    public function getProductSku(array $data): string
    {
        $sku = $this->helper->getSKUFormat($data);
        try {
            $product = $this->getProductBySku($sku);
        } catch (\Exception $e) {
            $product = $this->createNewProduct($sku, $data);
        }

        return $product->getSku();
    }

    public function aroundGetFileData(ImportModel $subject, \Closure $proceed)
    {
        $file = $this->getUploadedFile($subject);
        $fileExtension = $this->getFileExtension($file);
        if ($this->isValidSpreadsheet($fileExtension)) {
            return $this->helper->getSpreadSheetData($file);
        }

        return $proceed();
    }

    public function getOptionId(mixed $attribute, mixed $manufacturerLabel): mixed
    {
        $options = $attribute->getOptions();

        return $this->findOptionIdByLabel($options, $manufacturerLabel);
    }

    public function aroundImportData(ImportModel $subject, \Closure $proceed, array $data): void
    {
        $headerMapping = $subject->getHeaderMapping($data);
        foreach ($data as $row) {
            $mappedRow = $subject->getMappedData($headerMapping, $row);

            if ($this->isValidRow($mappedRow)) {
                $this->processMappedRow($subject, $mappedRow);
            }
        }

        $this->cleanCache();
    }

    private function hasSupplierName(array $result): bool
    {
        return isset($result['lieferant_name']) && ! empty($result['lieferant_name']);
    }

    private function processMappedData(array $result): array
    {
        $result['sku'] = $this->getProductSku($result);
        $result['serial_number'] = $result['voucher_code'];
        $result['voucher_external_link'] = $result['voucher_value_status_link'] ?? false;
        $result['supplier_name'] = $result['lieferant_name'];

        if ($this->hasEndDate($result)) {
            $result['end_date'] = $this->formatEndDate($result['end_date']);
        }

        $this->unsetUnnecessaryKeys($result);

        return $result;
    }

    private function hasEndDate(array $result): bool
    {
        return isset($result['end_date']) && ! empty($result['end_date']);
    }

    private function formatEndDate(string $endDate): string
    {
        return $endDate . ' 23:59:59';
    }

    private function unsetUnnecessaryKeys(array &$result): void
    {
        unset($result['voucher_value_status_link'], $result['voucher_code'], $result['lieferant_name']);
    }

    private function getProductBySku(string $sku)
    {
        return $this->productRepository->get($sku);
    }

    private function createNewProduct(string $sku, array $data)
    {
        $product = $this->productFactory->create([
            'data' => [
                'sku' => $sku,
                'has_serials' => true,
                'price' => $data['voucher_value'],
                'name' => $data['lieferant_name'] . ' ' . $data['voucher_value'],
            ],
        ]);
        $this->setProductAttributes($product, $data);
        $this->productRepository->save($product);

        return $product;
    }

    private function setProductAttributes($product, array $data): void
    {
        $this->setGeneralAttributes($product);
        $this->setStockAttributes($product);
        $this->setManufacturerAttribute($product, $data);
    }

    private function setGeneralAttributes($product): void
    {
        $product->setAttributeSetId(4);
        $product->setTypeId(Type::TYPE_VIRTUAL);
        $product->setVisibility(Visibility::VISIBILITY_BOTH);
        $product->setStatus(Status::STATUS_ENABLED);
    }

    private function setStockAttributes($product): void
    {
        $stockData = [
            'use_config_manage_stock' => 0,
            'is_in_stock' => 1,
            'manage_stock' => 0,
        ];
        $product->setStockData($stockData);
        $product->setQuantityAndStockStatus(['is_in_stock' => 1]);
    }

    private function setManufacturerAttribute($product, array $data): void
    {
        $product->setManufacturer($this->getManufacturer($data));
    }

    private function getUploadedFile(ImportModel $subject)
    {
        $file = $subject->getRequest()->getFiles('import_serial_numbers');
        if (! $file) {
            $this->messageManager->addErrorMessage(__('Please upload a file'));
        }

        return $file;
    }

    private function getFileExtension(array $file): string
    {
        return strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    }

    private function isValidSpreadsheet(string $fileExtension): bool
    {
        return in_array($fileExtension, ['xls', 'xlsx']);
    }

    private function findOptionIdByLabel(array $options, string $manufacturerLabel): mixed
    {
        foreach ($options as $option) {
            if ($this->isMatchingLabel($option, $manufacturerLabel)) {
                return $option->getValue();
            }
        }

        return null;
    }

    private function isMatchingLabel($option, string $manufacturerLabel): bool
    {
        return strtolower($option->getLabel()) === strtolower($manufacturerLabel);
    }

    private function getManufacturer($data)
    {
        $manufacturerLabel = $data['lieferant_name'];
        $attribute = $this->attributeRepository->get(Product::ENTITY, 'manufacturer');
        return $this->findOrCreateManufacturer($attribute, $manufacturerLabel);
    }

    private function findOrCreateManufacturer($attribute, $manufacturerLabel)
    {
        $optionId = $this->getOptionId($attribute, $manufacturerLabel);
        if (! $optionId) {
            $optionId = $this->createManufacturer($attribute, $manufacturerLabel);
        }

        return $optionId;
    }

    private function createManufacturer($attribute, $label)
    {
        $optionLabel = $this->createOptionLabel($label);
        $option = $this->createOption($optionLabel);

        $this->addOptionToAttribute($attribute, $option);

        return $this->getOptionId($attribute, $label);
    }

    private function createOptionLabel($label)
    {
        /** @var \Magento\Eav\Model\Entity\Attribute\OptionLabel $optionLabel */
        $optionLabel = $this->optionLabelFactory->create();
        $optionLabel->setStoreId(0);
        $optionLabel->setLabel($label);

        return $optionLabel;
    }

    private function createOption($optionLabel)
    {
        $option = $this->optionFactory->create();
        $option->setLabel($optionLabel->getLabel());
        $option->setStoreLabels([$optionLabel]);
        $option->setSortOrder(0);
        $option->setIsDefault(false);

        return $option;
    }

    private function addOptionToAttribute($attribute, $option): void
    {
        $this->attributeOptionManagement->add(
            Product::ENTITY,
            $attribute->getAttributeId(),
            $option
        );
    }

    private function isValidRow(array $mappedRow): bool
    {
        return ! empty($mappedRow['sku']) && ! empty($mappedRow['serial_number']);
    }

    private function processMappedRow(ImportModel $subject, array $mappedRow): void
    {
        $mappedRow['product_sku'] = $mappedRow['sku'];
        unset($mappedRow['sku']);
        $mappedRow['status'] = SerialNumberInterface::ACTIVE;

        if ($subject->getSerialNumberManagement()->isVoucher($mappedRow['serial_number'])) {
            $this->messageManager->addWarningMessage(__('This voucher already exists: %1', $mappedRow['serial_number']));
        } else {
            $this->processSerialNumber($subject, $mappedRow);
        }
    }

    private function processSerialNumber(ImportModel $subject, array $mappedRow): void
    {
        if (! $this->isValidDateRange($mappedRow)) {
            $this->messageManager->addErrorMessage(__(
                'The date format is incorrect in the importing row with the voucher code: %1',
                $mappedRow['serial_number']
            ));
        } else {
            $subject->getSerialNumberManagement()->addSerialNumber($mappedRow);
        }
    }

    private function isValidDateRange(array $mappedRow): bool
    {
        return $this->helper->checkDate($this->helper->splitDate($mappedRow['start_date'])) &&
            $this->helper->checkDate($this->helper->splitDate($mappedRow['end_date']));
    }

    private function cleanCache(): void
    {
        $this->helper->cleanEavCache();
        $this->helper->cleanFrontendCache();
    }
}
