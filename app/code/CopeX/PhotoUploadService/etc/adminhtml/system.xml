<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<tab id="copex" sortOrder="999" translate="label">
			<label>CopeX</label>
		</tab>
		<section id="photo_upload" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
			<label>Photo Upload</label>
			<tab>copex</tab>
			<resource>CopeX_PhotoUploadService::config_copex_photouploadservice</resource>
			<group id="general" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label>General</label>
				<field id="enable" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="select">
					<label>Enable</label>
					<comment>Enable the Module</comment>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
