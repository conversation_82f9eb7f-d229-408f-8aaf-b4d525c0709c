<?php

namespace CopeX\Firecheckout\Plugin\Magento\Checkout\Model;

use Cope<PERSON>\CreateCustomer\Helper\Address;
use CopeX\CreateCustomer\Helper\Customer;
use Cope<PERSON>\CreateCustomer\Model\AddressInfo;
use CopeX\CreateCustomer\Model\CustomerInfo;
use Magento\Customer\Model\AddressFactory;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Sales\Model\OrderFactory;

/**
 * Class GuestPaymentInformationManagement
 * @package CopeX\Firecheckout\Plugin\Magento\Checkout\Model
 */
class GuestPaymentInformationManagement
{
    const PREFIX = 'prefix';
    const EMAIL = 'email';
    const FIRSTNAME = 'firstname';
    const LASTNAME = 'lastname';
    const PASSWORD = 'password';
    const BILLING_ADDRESS = 'billingAddress';
    const SHOULD_CREATE_CUSTOMER = 'shouldCreateCustomer';

    /** @var Request */
    protected $request;

    /** @var Customer */
    protected $createCustomerHelper;

    /** @var Address */
    protected $addressHelper;
    /**
     * @var OrderFactory
     */
    protected $orderFactory;
    protected $addressFactory;
    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * GuestPaymentInformationManagement constructor.
     * @param Request        $request
     * @param Customer       $createCustomerHelper
     * @param Address        $addressHelper
     * @param OrderFactory   $orderFactory
     * @param AddressFactory $addressFactory
     */
    public function __construct(
        Request $request,
        Customer $createCustomerHelper,
        Address $addressHelper,
        OrderFactory $orderFactory,
        AddressFactory $addressFactory,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
    ) {
        $this->request = $request;
        $this->createCustomerHelper = $createCustomerHelper;
        $this->addressHelper = $addressHelper;
        $this->orderFactory = $orderFactory;
        $this->addressFactory = $addressFactory;
        $this->orderRepository = $orderRepository;
    }

    /**
     * set params to new created order and create shipping_address for customer if customer wants to have a account in checkout
     * @param \Magento\Checkout\Model\GuestPaymentInformationManagement $subject
     * @param int                                                       $result Order ID
     * @param string                                                    $cartId
     * @param string                                                    $email
     * @param PaymentInterface                                          $paymentMethod
     * @param AddressInterface|null                                     $billingAddress
     * @return int $result Order ID
     */
    public function afterSavePaymentInformationAndPlaceOrder(
        \Magento\Checkout\Model\GuestPaymentInformationManagement $subject,
        $result,
        $cartId,
        $email,
        $paymentMethod,
        $billingAddress = null
    ) {
        $requestContent = json_decode($this->request->getContent(), true);

        if (key_exists(self::SHOULD_CREATE_CUSTOMER, $requestContent) &&
            $requestContent[self::SHOULD_CREATE_CUSTOMER]) {
            $customerInfo = CustomerInfo::create();
            $addressInfoArray = [];

            if (key_exists(self::BILLING_ADDRESS, $requestContent)) {
                $customerInfo->setPrefix($requestContent[self::BILLING_ADDRESS][self::PREFIX]);
                $customerInfo->setFirstname($requestContent[self::BILLING_ADDRESS][self::FIRSTNAME]);
                $customerInfo->setLastname($requestContent[self::BILLING_ADDRESS][self::LASTNAME]);

                $addressInfoArray[] = AddressInfo::createWithAddressData(
                    $this->addressHelper->prepareAddressData($requestContent[self::BILLING_ADDRESS]),
                    'default_billing');
            }

            if (key_exists(self::PASSWORD, $requestContent) && $requestContent[self::PASSWORD]) {
                $customerInfo->setPassword($requestContent[self::PASSWORD]);
            }

            if (key_exists(self::EMAIL, $requestContent)) {
                $customerInfo->setEmail($requestContent[self::EMAIL]);
                $customer = $this->createCustomerHelper->createOrLoadCustomer(
                    $requestContent[self::EMAIL], $customerInfo, $addressInfoArray, $result);

                /* save also shipping Address on customer */
                if ($customer != null) {
                    $orderData = $this->orderFactory->create()->load($result);
                    $orderData->setCustomerId($customer->getId());
                    $orderData->setCustomerIsGuest(0);
                    $this->orderRepository->save($orderData);

                    if (!$orderData->getIsVirtual()) {
                        $address = $this->addressFactory->create();
                        $address->setData($orderData->getShippingAddress()->getData());

                        $address->setCustomerId($customer->getId())->setIsDefaultBilling('0')->setIsDefaultShipping('1')
                            ->setSaveInAddressBook('1');
                        $address->save();
                    }
                }
            }
        }

        return $result;
    }
}
