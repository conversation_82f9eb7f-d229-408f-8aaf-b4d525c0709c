<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route method="POST" url="/V1/carts/mine/create-customer-address">
        <service class="CopeX\Firecheckout\Api\CustomerAddressManagementInterface"
                 method="postCreateCustomerAddress"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route method="POST" url="/V1/carts/mine/update-customer-address">
        <service class="CopeX\Firecheckout\Api\CustomerAddressManagementInterface"
                 method="postUpdateCustomerAddress"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>