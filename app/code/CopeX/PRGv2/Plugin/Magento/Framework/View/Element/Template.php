<?php
/**
 * PRG masking for Magento 2
 * Copyright (C) 2018 CopeX
 * This file is part of CopeX/PRGv2.
 * CopeX/PRGv2 is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\PRGv2\Plugin\Magento\Framework\View\Element;

use CopeX\PRGv2\Model\Dom as Dom;

class Template
{

    protected $scopeConfig;

    protected $config = [];

    private $domParser;

    const COPEX_PRG_ROOT = "copex_prg";
    const REPLACE_BY_TEMPLATE = "replaceByTemplate";
    const REPLACE_BY_SELECTOR = "replaceBySelector";
    const NAME_IN_LAYOUT = "nameInLayout";
    const EXCLUDE = "exclude";
    const EXCLUDE_NAME_IN_LAYOUT = "excludeNameInLayout";

    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \CopeX\PRGv2\Model\Dom $domParser
    )
    {
        $this->scopeConfig = $scopeConfig;
        $this->domParser = $domParser;
        $this->setConfiguration();
    }

    public function setDom($str)
    {
        $dom = $this->domParser;
        if (empty($str) || strlen($str) > Dom::MAX_FILE_SIZE) {
            $dom->clear();
            return false;
        }
        $dom->load($str, false, false);
        return $dom;
    }

    /**
     * collects the
     */
    public function setConfiguration()
    {
        $this->config = $this->scopeConfig->getValue(self::COPEX_PRG_ROOT);
        $replaceConfig = [];
        if (isset($this->config[self::REPLACE_BY_SELECTOR])) {
            foreach ($this->config[self::REPLACE_BY_SELECTOR] as $selector) {
                $replaceConfig[$selector['template']] = $selector['classes'];
            }
        }
        $this->config[self::REPLACE_BY_SELECTOR] = $replaceConfig;
    }

    /**
     * @param \Magento\Framework\View\Element\Template $subject
     * @param                                          $result
     * @return mixed
     */
    public function afterToHtml(
        \Magento\Framework\View\Element\Template $subject,
        $result
    )
    {
        $result = $this->parseHtmlLinks($subject, $result);
        return $result;
    }

    /**
     * @param $subject
     * @param $result
     * @return mixed
     */
    protected function parseHtmlLinks($subject, $result)
    {
        $template = $subject->getTemplate() ?: "";
        $template = $this->normalizeTemplateName($template);
        if (is_array($this->config[self::EXCLUDE_NAME_IN_LAYOUT]) &&
            (in_array($subject->getNameInLayout(), $this->config[self::EXCLUDE_NAME_IN_LAYOUT]) ||
                in_array($template, $this->config[self::EXCLUDE]))) {
            $result = $this->addExclude($result);
        } elseif (is_array($this->config[self::REPLACE_BY_TEMPLATE]) &&
            in_array($template, $this->config[self::REPLACE_BY_TEMPLATE])) {
            $result = $this->replaceLinks($result);
        } elseif (is_array($this->config[self::NAME_IN_LAYOUT]) &&
            in_array($subject->getNameInLayout(), $this->config[self::NAME_IN_LAYOUT])) {
            $result = $this->replaceByNameInLayout($result);
        } elseif (array_key_exists($template, $this->config[self::REPLACE_BY_SELECTOR])) {
            $array = $this->config[self::REPLACE_BY_SELECTOR][$template];
            foreach ($array as $class) {
                $result = $this->replaceBySelector($result, $class);
            }
        }
        return $result;
    }

    public function normalizeTemplateName($template)
    {
        if (strstr($template, '::')) {
            //moduleName found in template
            return substr($template, (strpos($template, '::') + 2));
        }
        return $template;
    }

    /**
     * Parses HTML and encodes all a href http://"example.com" linktexts with base64
     * except tags with data-noparse attribute
     * sets HTML to parsed version
     * @param $html
     * @return mixed
     */
    public function replaceLinks($html, $find = "a")
    {
        $dom = $this->setDom($html);
        if ($dom) {
            $links = $dom->find($find);

            foreach ($links as &$link) {
                if ($this->canParseLink($link)) {
                    $maskedLink = $this->getMaskedHref($link);
                    $link->setAttribute('data-prg', $maskedLink);
                    $link->setAttribute('href', '');
                    $link->setAttribute('onclick', "return followPRGLink('".$maskedLink."')");
                }
            }
            return $dom->__toString();
        }
        return $html;
    }

    /**
     * Function to determine if a link can be parsed
     * @param $link
     * @return bool
     */
    protected function canParseLink($link)
    {
        return $link->hasAttribute('href')
            && substr($link->getAttribute('href'), 0, 1) != '#'
            && !$link->hasAttribute('data-prg')
            && !$link->hasAttribute('data-noparse');
    }

    /**
     * @param $link
     * @return string
     */
    protected function getMaskedHref($link)
    {
        return base64_encode(htmlspecialchars_decode(strip_tags($link->getAttribute('href'))));
    }

    /**
     * Parses a specific template and encodes all a href http://"example.com" linktexts from mentioned
     * classes with base64
     * ignores tags with data-noparse attribute
     * sets HTML to parsed version
     * @param $html
     * @param $find
     * @return mixed
     */
    public function replaceBySelector($html, $find)
    {
        return $this->replaceLinks($html, $find);
    }

    /**
     * Parses HTML and adds "data-noparse" attribute to all a elements after href
     * sets HTML to parsed version
     * @param $html
     * @return mixed
     */
    public function addExclude($html)
    {
        $dom = $this->setDom($html);
        $links = $dom->find('a');

        foreach ($links as &$link) {
            if ($link->hasAttribute('href') && !$link->hasAttribute('data-noparse')) {
                $link->setAttribute('data-noparse', '');
            }
        }
        return $dom->__toString();
    }

    public function replaceByNameInLayout($html)
    {
        return $this->replaceLinks($html);
    }
}
