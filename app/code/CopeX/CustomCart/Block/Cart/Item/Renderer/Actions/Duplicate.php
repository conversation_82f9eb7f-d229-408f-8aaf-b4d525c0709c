<?php

namespace CopeX\CustomCart\Block\Cart\Item\Renderer\Actions;

use CopeX\OrderCreator\Helper\Data;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Generic;
use Magento\Checkout\Helper\Cart;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Plissee\Base\Helper\Configurator;
use Swissup\Hreflang\Helper\Store;

/**
 * @api
 * @since 100.0.2
 */
class Duplicate extends Generic
{
    /**
     * @var Cart
     */
    protected $cartHelper;
    /**
     * @var Configurator
     */
    private $configuratorHelper;
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    /**
     * @var Store
     */
    private $storeHelper;

    /**
     * @param Context               $context
     * @param Cart                  $cartHelper
     * @param Configurator          $configuratorHelper
     * @param StoreManagerInterface $storeManager
     * @param Store                 $storeHelper
     * @param array                 $data
     * @codeCoverageIgnore
     */
    public function __construct(
        Template\Context $context,
        Cart $cartHelper,
        Configurator $configuratorHelper,
        StoreManagerInterface $storeManager,
        Store $storeHelper,
        array $data = []
    ) {
        $this->cartHelper = $cartHelper;
        parent::__construct($context, $data);
        $this->configuratorHelper = $configuratorHelper;
        $this->storeManager = $storeManager;
        $this->storeHelper = $storeHelper;
    }

    /**
     * Get item configure url
     * @return bool
     */
    public function isDuplicatable()
    {
        return $this->getItem()->getProduct()->getTypeId() == Data::PRODUCT_TYPE;
    }

    /**
     * Get item configure url
     * @return string
     */
    public function getDuplicateUrl()
    {
        $articleNumber = $this->configuratorHelper->getArticleNumber($this->getItem()->getSku());
        $sku = $this->getItem()->getSku();
        $storeCode = $this->storeManager->getStore()->getCode();
        if ($this->isApiProduct()) {
            return $this->getBaseUrl() . "configurator?sku=" . $sku . "&articleNumber=" . $articleNumber .
                   "&storeCode=" . $storeCode . "&categoryId=" . $this->getItem()->getConfiguratorCategoryId();
        }
        return "";
    }

    /**
     * @return bool
     */
    public function isApiProduct()
    {
        return $this->getItem()->getProduct()->getTypeId() == Data::PRODUCT_TYPE;
    }
}
