<?php

declare(strict_types=1);

namespace CopeX\OptionFeatures\Observer;

use CopeX\OptionFeatures\Helper\Data as Helper;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ResourceModel\Product\Option\Collection as OptionCollection;
use Magento\Catalog\Model\ResourceModel\Product\Option\Value\Collection as OptionValueCollection;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\DataObject;
use Magento\Framework\DB\Select;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote\Item;
use MageWorx\OptionBase\Helper\Data as BaseHelper;

class CollectQuoteItemCost implements ObserverInterface
{
    protected OptionValueCollection $optionValueCollection;
    protected OptionCollection $optionCollection;
    protected Helper $helper;
    protected BaseHelper $baseHelper;

    public function __construct(
        OptionValueCollection $optionValueCollection,
        OptionCollection $optionCollection,
        Helper $helper,
        BaseHelper $baseHelper
    ) {
        $this->optionValueCollection = $optionValueCollection;
        $this->optionCollection = $optionCollection;
        $this->helper = $helper;
        $this->baseHelper = $baseHelper;
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Quote\Model\Quote\Item $quoteItem */
        $quoteItem = $observer->getEvent()->getQuoteItem();
        if (! $this->validateItem($quoteItem)) {
            return $this;
        }
        $originalQuoteItem = $quoteItem;
        $originalCost = 0;
        $originalWeight = 0;
        $product = $this->resolveProductAndCost($quoteItem, $originalCost, $originalWeight);
        /** @var \Magento\Framework\DataObject $buyRequest */
        $buyRequest = $quoteItem->getBuyRequest();
        if (! $buyRequest || ! $buyRequest->getOptions()) {
            return $this;
        }
        $qty = $this->getOriginalQtyFromBuyRequest($buyRequest);
        [$cost, $weight] = $this->calculateOptionCostsAndWeight($product, $buyRequest, $qty, $originalWeight);
        if ($this->isAbsoluteCostEnabled($product)) {
            $originalCost = 0;
        }
        if ($this->isAbsoluteWeightEnabled($product)) {
            $originalWeight = 0;
        }
        $resultCost = (float) ($originalCost + $cost);
        if ($this->helper->isCostEnabled()) {
            $quoteItem->setBaseCost($resultCost);
        }
        if ($this->isWeightEnabled($product)) {
            $resultWeight = (float) ($originalWeight + $weight);
            $quoteItem->setWeight($resultWeight);
            $quoteItem->setRowWeight($resultWeight * $qty);
        }

        return $this;
    }

    protected function getValuesCollection($optionIds): array
    {
        $this->optionValueCollection->addOptionToFilter($optionIds);
        $values = $this->optionValueCollection->getItems();
        $this->optionValueCollection->clear()->getSelect()->reset(Select::WHERE);

        return $values;
    }

    protected function getProductOptions(Product $product): array
    {
        $linkField = $this->baseHelper->getLinkField(ProductInterface::class);
        $this->optionCollection->reset();
        $this->optionCollection->addProductToFilter($product->getData($linkField));

        return $this->optionCollection->getItems();
    }

    protected function prepareOptionValues($optionValue)
    {
        if (! is_array($optionValue)) {
            $optionValues = explode(',', $optionValue);
        } else {
            $optionValues = $optionValue;
        }

        return $optionValues;
    }

    protected function validateItem(Item $quoteItem): bool
    {
        if (! $this->helper->isWeightEnabled() && ! $this->helper->isCostEnabled()) {
            return false;
        }
        if (! $quoteItem->getOptions()) {
            return false;
        }
        $buyRequest = $quoteItem->getBuyRequest();
        if (! $buyRequest || ! $buyRequest->getOptions()) {
            return false;
        }

        return true;
    }

    protected function getOriginalQtyFromBuyRequest(DataObject $buyRequest): float|int
    {
        return $buyRequest->getOriginalQty() && $buyRequest->getOriginalQty() > 0.0001 ? (float) $buyRequest->getOriginalQty() : 1;
    }

    protected function isAbsoluteCostEnabled(Product $product): bool
    {
        return $product->getData(Helper::KEY_ABSOLUTE_COST) === Helper::ABSOLUTE_COST_TRUE && $this->helper->isAbsoluteCostEnabled();
    }

    protected function isAbsoluteWeightEnabled(Product $product): bool
    {
        return $product->getData(Helper::KEY_ABSOLUTE_WEIGHT) === Helper::ABSOLUTE_WEIGHT_TRUE && $this->helper->isAbsoluteWeightEnabled();
    }

    protected function isWeightEnabled(Product $product): bool
    {
        return $product->getTypeInstance()->hasWeight() && $this->helper->isWeightEnabled();
    }

    private function resolveProductAndCost($quoteItem, &$originalCost, &$originalWeight)
    {
        $parentItem = $quoteItem->getParentItem();
        if ($parentItem === null && $quoteItem->getProductType() === Configurable::TYPE_CODE) {
            return $this->processConfigurableProduct($quoteItem, $originalCost, $originalWeight);
        }
        if ($parentItem) {
            return $this->processParentItem($quoteItem, $originalCost, $originalWeight);
        }
        $product = $quoteItem->getProduct();
        $originalCost = $product->getData('cost');
        $originalWeight = $product->getData('weight');

        return $product;
    }

    private function processConfigurableProduct($quoteItem, &$originalCost, &$originalWeight)
    {
        $product = $quoteItem->getProduct()->getCustomOption('simple_product')->getProduct();
        if (! $product) {
            return null;
        }
        $originalCost = $product->getData('cost');
        $originalWeight = $product->getData('weight');

        return $product;
    }

    private function processParentItem($quoteItem, &$originalCost, &$originalWeight)
    {
        $originalQuoteItem = $quoteItem;
        $quoteItem = $quoteItem->getParentItem();
        if (! $quoteItem->getOptions()) {
            return null;
        }
        $product = $quoteItem->getProduct();
        $originalProduct = $originalQuoteItem->getProduct();
        $originalCost = $originalProduct->getData('cost');
        $originalWeight = $originalProduct->getData('weight');

        return $product;
    }

    private function calculateOptionCostsAndWeight($product, $buyRequest, $qty, $originalWeight): array
    {
        $cost = 0;
        $weight = 0;
        $options = $buyRequest->getOptions();
        $optionsItems = $this->getProductOptions($product);
        $values = $this->getValuesCollection(array_keys($options));
        foreach ($options as $optionId => $optionValue) {
            $option = $optionsItems[$optionId] ?? null;
            if (! $option || ! $this->baseHelper->isSelectableOption($option->getType())) {
                continue;
            }
            $optionQty = $this->getOptionQty($buyRequest, $optionId);
            [$optionCost, $optionWeight] = $this->calculateOptionValueCostAndWeight($optionValue, $values, $optionQty, $originalWeight);
            if ($option->getData(Helper::KEY_ONE_TIME) && $this->helper->isOneTimeEnabled()) {
                $optionCost /= $qty;
                $optionWeight /= $qty;
            }
            $cost += $optionCost;
            $weight += $this->applyMinWeightRule($values, $optionWeight, $qty, $optionValue);
        }

        return [$cost, $weight];
    }

    private function getOptionQty($buyRequest, $optionId)
    {
        return $this->helper->isQtyInputEnabled() && $buyRequest->getOptionsQty($optionId)
            ? $buyRequest->getOptionsQty($optionId)
            : 1;
    }

    private function calculateOptionValueCostAndWeight($optionValue, $values, $optionQty, $originalWeight): array
    {
        $optionCost = 0;
        $optionWeight = 0;
        foreach ($this->prepareOptionValues($optionValue) as $valueId) {
            $value = $values[$valueId] ?? null;
            if (! $value) {
                continue;
            }
            if (is_array($optionQty)) {
                $optionQty = $optionQty[$valueId] ?? 0;
            }
            $optionCost += $value->getData(Helper::KEY_COST) * $optionQty;
            if ($value->getData(Helper::KEY_WEIGHT_TYPE) !== 'percent') {
                $optionWeight += $value->getData(Helper::KEY_WEIGHT) * $optionQty;
            } else {
                $optionWeight += ($originalWeight * $value->getData(Helper::KEY_WEIGHT) / 100) * $optionQty;
            }
        }

        return [$optionCost, $optionWeight];
    }

    private function applyMinWeightRule($values, $optionWeight, $qty, $selectedOptionValue)
    {
        if ($this->helper->isMinWeightEnabled()) {
            $value = $values[$selectedOptionValue];
            $baseWeight = floatval($value->getWeight());
            $minWeight = floatval($value->getMinWeight());
            if ($minWeight > 0 && ($baseWeight * $qty) < $minWeight) {
                return floatval($minWeight / $qty);
            }
        }

        return $optionWeight;
    }
}
