<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2021.  Roman Hutterer CopeX GmbH | https://copex.io | <<EMAIL>>
  -->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="navbar.logo">
            <arguments>
                <argument name="logo_file" xsi:type="string"><![CDATA[images/logo-mj.svg]]></argument>
                <argument name="logo_img_width" xsi:type="number">277</argument>
                <!--                    <argument name="logo_img_height" xsi:type="number">44</argument>-->
            </arguments>
        </referenceBlock>
        <referenceBlock name="store.logo">
            <arguments>
                <argument name="logo_file" xsi:type="string"><![CDATA[images/logo-mj.svg]]></argument>
                <argument name="logo_img_width" xsi:type="number">165</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
