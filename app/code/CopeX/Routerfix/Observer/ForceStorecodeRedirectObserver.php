<?php

namespace CopeX\Routerfix\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\Filesystem\DirectoryList;

class ForceStorecodeRedirectObserver implements ObserverInterface
{
    protected $storeManager;
    protected $url;
    /** @var array $storeCodes - array of existing storecodes */
    protected $storeCodes = [
        'de-at',
        'de-de',
        'de-ch'
    ];
    /**
     * @var \Magento\Framework\Filesystem
     */
    private $filesystem;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\UrlInterface $url,
        \Magento\Framework\Filesystem $filesystem,
        \Psr\Log\LoggerInterface $logger
    )
    {
        $this->storeManager = $storeManager;
        $this->url = $url;
        $this->filesystem = $filesystem;
        $this->logger = $logger;
    }

    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $observer->getEvent()->getRequest();
        $isApirequest = $request->getControllerModule() == 'Mage_Api';
        $isPayoneRequest = $request->getControllerModule() == 'Payone_Core';
        $isHogastRequest = $request->getControllerModule() == 'CopeX_Hogast';

        if ($request->isAjax() || $request->isPost() || $isApirequest || $isPayoneRequest || $isHogastRequest) {
            return;
        }
        $urlParts = parse_url($this->url->getCurrentUrl());
        $path = $urlParts['path'];
        $isStoreSwitch = ($path == "/stores/store/switchrequest");
        if($isStoreSwitch){
            return;
        }

        // get storecode from URL
        $urlCode = trim(substr($path, 0, 6), '/');


        // If path does not already contain an existing storecode
        if ($path == '/' && $this->_bot_detected()) {
            $path = $this->filesystem->getDirectoryRead(DirectoryList::PUB)->getAbsolutePath();
            echo file_get_contents($path . 'x-default.html');
            die();
        }

        if (!in_array($urlCode, $this->storeCodes)) {
            $path = ltrim($path, '/');
            if ($path == 'de-at') {
                $path = '';
            }


            // Redirect to URL including storecode
            header("HTTP/1.1 301 Moved Permanently");
            header("Location: " . $this->storeManager->getStore()->getBaseUrl() . $path);
            exit();
        }
    }

    function _bot_detected() {

        return (
            isset($_SERVER['HTTP_USER_AGENT'])
            && preg_match('/bot|crawl|slurp|spider|mediapartners/i', $_SERVER['HTTP_USER_AGENT'])
        );
    }
}
