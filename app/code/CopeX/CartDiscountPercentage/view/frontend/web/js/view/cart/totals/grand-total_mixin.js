define([
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/totals',
        'jquery'
    ],
    function (quote, totals, $) {
        'use strict';

        var mixin = {
            /**
             * Get pure value.
             */
            getPureValue: function () {
                var price = 0;
                price = totals.getSegment('grand_total').value;
                return price;
            },

            /**
             * @return {*|String}
             */
            getValue: function () {
                return this.getFormattedPrice(this.getPureValue());
            },
            getTaxRateLabel: function () {
                let rate = 0;
                let taxesSection = totals.getSegment("tax");
                if (taxesSection.value > 0) {
                    rate = taxesSection.extension_attributes.tax_grandtotal_details[0].rates[0].percent;
                }
                return $.mage.__('Price incl. %1% VAT').replace('%1', rate);
            },
        };

        return function (target) {
            if (!checkoutConfig.isFirecheckout) {
                return target;
            }
            return target.extend(mixin);
        };
    });
