<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer instance="CopeX\MontageService\Observer\Sales\ModelServiceQuoteSubmitBefore"
                  name="copex_montageservice_observer_sales_modelservicequotesubmitbefore_sales_model_service_quote_submit_before"/>
    </event>
    <event name="should_display_montageservice_block">
        <observer instance="CopeX\MontageService\Observer\MontageserviceHideFixation"
                  name="copex_montageservice_observer_montageservice_fixation_check"/>
    </event>
</config>
