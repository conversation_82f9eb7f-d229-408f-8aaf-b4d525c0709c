<?php
/**
 * A Magento 2 module named CopeX/PriceFromSimple
 * Copyright (C) 2017 Roman Hutterer
 * 
 * This file included in CopeX/PriceFromSimple is licensed under OSL 3.0
 * 
 * http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 * Please see LICENSE.txt for the full text of the OSL 3.0 license
 */

namespace CopeX\PriceFromSimple\Plugin\Magento\ConfigurableProduct\Pricing\Price;

class FinalPrice
{

    public function afterGetValue(
        \Magento\ConfigurableProduct\Pricing\Price\FinalPrice $subject,
        $result
    ) {
        $product = $subject->getProduct();
        $productType = $product->getTypeInstance();
        if(!$productType instanceof \Magento\ConfigurableProduct\Model\Product\Type\Configurable){
            return $result;
        }
        $simpleConfig = [];
        if(!count($_GET)){
            return $result;
        }
        foreach($_GET as $key => $value){
            if(is_numeric($key)){
                $simpleConfig[$key] = $value;
            }
        }

        $simpleProduct = $productType->getProductByAttributes($simpleConfig, $product);
        if($simpleProduct instanceof \Magento\Catalog\Model\Product){
            return $simpleProduct->getFinalPrice();
        }
        return $result;
    }
}
