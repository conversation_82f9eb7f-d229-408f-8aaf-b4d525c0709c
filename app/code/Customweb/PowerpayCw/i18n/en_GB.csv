"'Payment Period' must be specified in days, @value given.","'Payment Period' must be specified in days, @value given."
"(Test)","(Test)"
"* Required Fields","* Required Fields"
"-- No Status Change --","-- No Status Change --"
"3D Secure","3D Secure"
"3D Secure Authentication","3D Secure Authentication"
"3D Secure failed","3D Secure verification failed"
"3D Secure sucess","3D Secure successfull"
"<b>IBAN:</b> !iban<br/><b>Payment Reason:</b> !reason","<b>IBAN:</b> !iban<br/><b>Payment Reason:</b> !reason"
"@message","@message"
"@reason","@reason"
"A authorized transaction cannot be authorized again.","An authorised transaction cannot be authorised again."
"A authorized transaction cannot be marked as failed.","An authorised transaction cannot be marked as failed."
"A cancelled transaction cannot be authorized.","A cancelled transaction cannot be authorised."
"A cancelled transaction cannot be cancelled again.","A cancelled transaction cannot be cancelled again."
"A cancelled transaction cannot be captured.","A cancelled transaction cannot be captured"
"A cancelled transaction cannot be marked as failed.","A cancelled transaction cannot be marked as failed."
"A cancelled transaction cannot be refunded.","A cancelled transaction cannot be refunded"
"A captured transaction cannot be authorized.","A captured transaction cannot be authorised."
"A captured transaction cannot be cancelled.","A captured transaction cannot be cancelled."
"A captured transaction cannot be marked as failed.","A captured transaction cannot be marked as failed."
"A failed authorization cannot be authorized.","A failed authorisation cannot be authorised."
"A payment method may not available for customers from different counties. You can decide when this availability check should happen. We recommend to set it during authorization","Some payment methods are not be available for customers from certain counties. You can decide at what instant this availability check should happen. We recommend setting it to 'during authorisation'."
"A refund was added over !amount.","A refund was added over !amount."
"A transaction may be uncertain, when the payment is not guaranteed. For example in case the credit card does not participate in the 3D procedure.","A transaction may be uncertain, when the payment is not guaranteed. For example in the case that the credit card does not participate in the 3D Secure procedure."
"About","About"
"Account Holder Name","Account holder"
"Account owner name.","Account holder"
"Action","Action"
"Actions","Actions"
"Activate","Active"
"Activate Open Invoice as Payment Method. With the setting validation you can control if the credit scoring is done before the payment method is displayed or afterwards.","Activate Open Invoice as Payment Method. With the setting validation you can control if the credit scoring is done before the payment method is displayed or afterwards."
"Active","Active"
"Additional Information","Additional Information"
"Adjust Qty","Adjust Quantity"
"Adjustment Discount","Adjustment Discount"
"Adjustment Fee","Adjustment Fee"
"Adjustment Refund is not supported.","Adjustment Refund is not supported."
"After payment selection","After payment method selection"
"After selecting payment method","After payment method selection"
"Ajax authorization not supported.","Ajax authorization not supported"
"Alias","Alias"
"Alias For Display","Alias For Display"
"All Customers","All Customers"
"Allowed Currencies","Allowed currencies"
"Allowed currencies for this payment method","Permitted currencies for this payment method"
"Already registered?","Already registered?"
"Always Checkout as Guest","Always Checkout as Guest"
"American Express","American Express"
"Amount","Amount"
"Amount Incl. Tax","Amount Incl. Tax"
"An error occurred during two-factor authentication.","An error occurred during two-factor authentication."
"An error occurred in the communication between the shop and POWERPAY. You may want to check the credentials.","An error occurred in the communication between the shop and POWERPAY. You may want to check the credentials."
"Approved","Approved"
"Are you sure you want to cancel this order?","Are you sure you want to cancel this order?"
"Authentication Password","Authentication Password"
"Authentication Password (MOTO)","Authentication Password (MOTO)"
"Authentication Password (MOTO) (Test)","Authentication Password (MOTO) (Test)"
"Authentication Password (Test)","Authentication Password (Test)"
"Authentication User","Authentication User"
"Authentication User (MOTO)","Authentication User (MOTO)"
"Authentication User (MOTO) (Test)","Authentication User (MOTO) (Test)"
"Authentication User (Test)","Authentication User (Test)"
"Author","Author"
"Authorization Amount","Authorisation Amount"
"Authorization Failed","Authorisation failed"
"Authorization Method","Authorisation Method"
"Authorization code was not set in response.","Authorization code was not set in response."
"Authorization is uncertain.","Authorisation is uncertain"
"Authorized Status","Authorised status"
"Available credit empty.","Available credit empty."
"Await Notification","Await Notification"
"Awaiting confirmation","Awaiting confirmation"
"BIC","BIC"
"Bad signature format.","False signature format."
"Bank Account","Bank Account"
"Bank Address","Bank Address"
"Bank Location","Bank Location"
"Bank Name","Bank Name"
"Bank account number","Bank account number (IBAN)"
"Bank code number","Bank code number"
"Before payment selection","Before selection of the payment method"
"Before selecting payment method","Before selecting payment method"
"Beneficiary First Line","Beneficiary First Line"
"Beneficiary Fourth Line","Beneficiary Fourth Line"
"Beneficiary Second Line","Beneficiary Second Line"
"Beneficiary Third Line","Beneficiary Third Line"
"Bill delivery via POWERPAY","Bill delivery via POWERPAY"
"Bill delivery via merchant","Bill delivery via merchant"
"Billing Address","Billing Address"
"Billing Information","Billing information"
"Billing address","Billing address"
"Blocked card","Blocked card"
"Branch (Filial) ID, supplied by MF Group.","Branch (Filial) ID, supplied by POWERPAY."
"Branch ID","Branch ID"
"Branch ID (MOTO)","Branch ID (MOTO)"
"Branch ID (MOTO) (Test)","Branch ID (MOTO) (Test)"
"Branch ID (Test)","Branch ID (Test)"
"Brand Name","Brand Name"
"By setting the capturing the reservation can be captured directly after the order or later manually over the backend of the store.","With the capture setting, the transaction can be captured directly after the order or later manually over the backend of the store."
"CVC Code","CVC Code"
"Cancel","Cancel"
"Cancel ID","Cancel ID"
"Cancel pending orders when out of stock","Cancel pending orders when out of stock"
"Cancel status","Cancel status"
"Canceled PowerpayCw","Canceled PowerpayCw"
"Canceling of this payment not possible!","Cancellation of this payment not possible!"
"Cancelled Status","Cancelled status"
"Cannot update item quantity.","Cannot update item quantity."
"Capture Amount","Amount to capture"
"Capture ID","Capture ID"
"Capture amount","Capture amount"
"Capture information","Capture information"
"Capture status","Capture status"
"Capture: Don't Close","Capture: Don't close"
"Captured Amount","Captured amount"
"Captured Status","Captured status"
"Captures","Captures"
"Captures & Refunds","Captures & Refunds"
"Capturing","Capturing"
"Card Expiration","Expiry Date"
"Card Holder Name","Card holder name"
"Card Number","Card number"
"Card already active","Card already active"
"Card has movement","Card has movement"
"Card number was not set in response.","Card number was not set in response."
"Carrier Title","Carrier Title"
"Check out faster","Check out faster"
"Check to activate the external checkout.","Check to activate the external checkout."
"Checkout","Checkout"
"Checkout as Guest","Checkout as a guest"
"Checkout as a Guest or Register","Checkout as a guest or register"
"Checkout as a new customer","Checkout as a new customer"
"Checkout using your account","Checkout using your account"
"Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status","Choose settlement after order in case you want an invoice to be created with your order. The status of the invoice will be set according to your specified capture status."
"Choose settlement after order in case you want an invoice to be created with your oder. The state of the invoice will set according to your capture status. For captured payments, an invoice is always created.","Choose settlement after order in case you want an invoice to be created with your order. The status of the invoice will be set according to your specified capture status. For captured payments, an invoice is always created."
"Code @code could not be mapped to a message.","Code @code could not be mapped to a message."
"Company","Company"
"Company Commercial Number","Company Commercial Number"
"Company Name","Company Name"
"Company must be empty.","Company must be empty."
"Confirm Password","Confirm Password"
"Confirmation / Settlement","Confirmation / Settlement"
"Continue","Continue"
"Could not convert object of type '!type' to string.","Could not convert object of type '!type' to string."
"Could not extract conversation, conversation_xml contains full confirmation.","Could not extract conversation, conversation_xml contains full confirmation."
"Could not find a brand for the given card number.","Could not find a brand for the given card number."
"Could not find pressed button.","Could not find pressed button."
"Could not find template file at default location '!location'.","Could not find template file at default location '!location'."
"Could not find the payment method !paymentMethodName.","Could not find the payment method !paymentMethodName."
"Could not load layout from URL !url. Reason: !reason","Could not load layout from URL !url. Reason: !reason"
"Country @country is not supported.","Country @country is not supported."
"Create Account","Create Account"
"Create New Order - Payment","Create New Order - Payment"
"Create a recurring payment, with an existing alias is not supported","Creating a recurring payment with an existing alias is not supported."
"Creating an account has many benefits:","Creating an account has many benefits:"
"Creation Date","Creation Date"
"Credit Card Brands","Credit Card Brands"
"Credit Memo History","Credit Memo History"
"Credit Memo Totals","Credit Memo Totals"
"Credit memo's total must be positive.","Credit memo's total must be positive."
"Creditor Address Line 1","Creditor Address Line 1"
"Creditor Country","Creditor Country"
"Creditor Iban","Creditor Iban"
"Creditor Name","Creditor Name"
"Currency","Currency"
"Currency @currency is not supported","Currency @currency is not supported."
"Current Configuration Scope:","Current Configuration Scope:"
"Customer Account","Customer account"
"Customer Alias Management","Customer Alias Management"
"Customer successfully returned from PowerpayCw","Customer successfully returned from PowerpayCw"
"Customer successfully returned from POWERPAY","Customer successfully returned from the PowerpayCw payment page."
"Customer sucessfully returned from the PowerpayCw payment page.","Customer successfully returned from the PowerpayCw payment page."
"Customers","Customers"
"Customweb","Customweb"
"Date","Date"
"Date must be set.","Date must be set."
"Date of Birth","Date of birth"
"Day","Day"
"Deactivate","Deactivate"
"Dear customer we have received your billing and shipping information. In order to finish your order please create an account below or checkout as guest.","Dear customer we have received your billing and shipping information. In order to finalize your order please create an account below or checkout as a guest."
"Dear customer, it appears to us that your payment was successful, but we are still waiting for confirmation. You can wait on this page and we will redirect you after we received the confirmation. Or you can close this window and we will send out an order confirmation email.","Dear customer, it appears to us that your payment was successful, but we are still waiting for confirmation. You can wait on this page and we will redirect you after we received the confirmation. Or you can close this window and we will send out an order confirmation email."
"Decode failed with message @message","Decode failed with message @message"
"Default Config","Default Configuration"
"Deferred","Deferred"
"Deferred (authorization)","Deferred (authorisation)"
"Deferred capturing","Deferred capturing"
"Deferred settlement","Deferred settlement"
"Define a maximal order total for this checkout to be available.","Define a maximal order total for this checkout to be available."
"Define a minimal order total for this checkout to be available.","Define a minimal order total for this checkout to be available."
"Delayed","Deferred"
"Delete","Delete"
"Delivery address","Delivery address"
"Description","Description"
"Diners Club","Diners Club"
"Direct Debits","Direct Debits (ELV)"
"Direct E-Banking","Direct E-Banking"
"Direct capture after order","Direct capturing"
"Directly after order","Direct capturing"
"Directly after order (sale)","Direct capturing"
"Disabled","Deactivated"
"Discount","Discount"
"Discount Amount","Discount Amount"
"Don't change order status","Don't change order status"
"During authorization","During the authorisation"
"Edit","Edit"
"Edit Invoice %s","Edit Invoice %s"
"Email Address","Email Address"
"Empty version number field name","Empty version number field name"
"Enable or disable logging.","Enable or disable logging."
"Enable the payment method","Enable this payment method"
"Enabled","Enabled"
"Enter the Merchant ID, Branch ID, Terminal ID and Authentication User & Password that you will receive from POWERPAY.","Enter the Merchant ID, Branch ID, Terminal ID and Authentication User & Password that you will receive from POWERPAY."
"Enter the date on which your card expires.","Enter the card expiry date."
"Error with the length of the validation date.","Error with the length of the validation date."
"Excl. Tax","Excl. Tax"
"Expired card","Expired card"
"External Checkout","External Checkout"
"External Checkout: Guest Checkout","External Checkout: Guest Checkout"
"Failed","Failed"
"Failed payment authorization.","The authorization failed."
"Female","Female"
"Figure !number:","Figure !number:"
"Filial ID","Filial ID"
"Filial ID (MOTO)","Filial ID (MOTO)"
"Forgot Your Password?","Forgot Your Password?"
"Forgot your password?","Forgot your password?"
"Foribdden operation","Forbidden operation"
"Funds too high","The funds are too high."
"Funds too low","The funds are too low."
"Gender","Gender"
"Gender must be male or female, @gender supplied.","Gender must be male or female, @gender supplied."
"Gender must be male, female or company, @gender supplied.","Gender must be male, female or company, @gender supplied."
"Get Update","Get Update"
"Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to Authorize.net. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id}).","Here you can insert an order prefix. The prefix allows you to change the order number that is transmitted to POWERPAY. The prefix must contain the tag {id}. It will then be replaced by the order number (e.g. name_{id})."
"Here you can modify what the order number looks like. The placeholder {id} will be replaced with the internal order number (e.g. 'MyShop-{id}').","Here you can modify what the order number looks like. The placeholder {id} will be replaced with the internal order number (e.g. 'MyShop-{id}')."
"Hide","Hide"
"How long should the customer be able to pay the invoice (for Order Invoice).","How long should the customer be able to pay the invoice (for Order Invoice)."
"How the invoice should be processed.","How the invoice should be processed."
"IBAN","IBAN"
"IBAN number","Bank account number (IBAN)"
"If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore.","If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, the item will be reserved until the payment is cancelled (see timeout option below). Activate this option if you do NOT wish to reserve the item in case of an aborted or failed payment. The order will then be cancelled immediately."
"If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore. Be careful though: If you enable this option, there is a chance that payments are made with the corresponding order being already canceled.","If a product is in stock only once and the payment of the customer ordering this product fails or is aborted, it won't be available until the order is canceled (see timeout option below). Enable this setting to cancel the customer's pending orders when he returns to the cart page and a product is not available anymore. Be careful though: If you enable this option, there is a chance that payments are made with the corresponding order already being canceled."
"If it is not applicable leave the field empty.","If it is not applicable leave the field empty."
"If the status is 'Pending' it is unclear whether the transaction will be cancelled successfully.","If the status is 'Pending' it is unclear whether the transaction will be cancelled successfully."
"If the status is 'Pending' it is unclear whether the transaction will be captured successfully.","If the status is 'Pending' it is unclear whether the transaction will be captured successfully."
"If the status is 'Pending' it is unclear whether the transaction will be refunded successfully.","If the status is 'Pending' it is unclear whether the transaction will be refunded successfully."
"If the test mode is selected the test account is used. Otherwise the configured account is used.","If the test mode is selected the test account is used. Otherwise the configured account is used."
"If you have any issues with the module please feel free to contact us. See our <a href='https://www.sellxed.com/en/support' target='_blank'>support page</a> for more information.","If you have any issues with the module please feel free to contact us. See our <a href='https://www.sellxed.com/en/support' target='_blank'>support page</a> for more information."
"If you would like to use recurring payments, you need our magento module ""Customweb Subscription"". For further information go to <a href=""http://www.sellxed.com/shop/"" target=""_blank"">sellxed.com</a>.","If you would like to use recurring payments, you need our Magento module ""Customweb Subscription"". For further information go to <a href=""http://www.sellxed.com/shop/"" target=""_blank"">sellxed.com</a>."
"Image Brand Selection","Image Brand Selection"
"In some cases it may take a few seconds until the transaction is successfully authorized. When this option is enabled, the notification is awaited.","In some cases it may take a few seconds until the transaction is successfully authorized. When this option is enabled, the notification is awaited."
"In some cases the transaction requires a few seconds until it is successfully authorized. When this option is enabled the user is hold until the transaction reach the final state. If this is not the case in 30 seconds a message is shown to the customer, which explains the situation.","In some cases the transaction requires a few seconds until it is successfully authorized. When this option is enabled the user is on hold until the transaction reaches the final state. If this is not the case in 30 seconds a message is shown to the customer, which explains the situation."
"In test mode only users with the first name 'Good' and the last name 'Customer' will pass the validation.","In test mode only users with the first name 'Good' and the last name 'Customer' will pass the validation.  All other adresses and customer names will be ignored."
"Inactive","Inactive"
"Inactive card","Inactive card"
"Incl. Tax","Incl. Tax"
"Information","Information"
"Internal error","Internal error"
"Invalid authorization code","Invalid authorization code"
"Invalid card type","Invalid card type"
"Invalid login or password.","Invalid login or password."
"Invoice Items","Invoice Items"
"Invoice Settlement","Invoice Settlement"
"Invoice Totals","Invoice Totals"
"Invoice Type","Invoice Type"
"Invoice settlement","Invoice settlement"
"Invoice was created successfully","Invoice was successfully created"
"Invoice with partial payments (POWERPAY) currently not possible. You can inform yourself on the reason <a href='!url' target='_blank'>here</a>.","Invoice with partial payments (POWERPAY) currently not possible. You can inform yourself on the reason <a href='!url' target='_blank'>here</a>."
"Invoice with partial payments (POWERPAY) currently not possible. You can inform yourself on the reason <a href='https://www.powerpay.ch/support/'>here</a>.","Invoice with partial payments (POWERPAY) is currently not possible. You can inform yourself about the reason here: <a href='https://www.powerpay.ch/support/'>https://www.powerpay.ch/support/</a>."
"Invoices","Invoices"
"Issue Number","Issue Number"
"It is not possible to checkout in your country.","It is not possible to checkout in your country."
"Item","Item"
"Items","Items"
"Items Refunded","Items Refunded"
"JCB","JCB"
"Live HMAC Key","Live HMAC Key"
"Live Identifier (PBX_IDENTIFIANT)","Live Identifier (PBX_IDENTIFIANT)"
"Live Mode","Live Mode"
"Live Password","Live Password"
"Live Rang (PBX_RANG)","Live Rang (PBX_RANG)"
"Live Site ID (PBX_SITE)","Live Site ID (PBX_SITE)"
"Logging","Logging"
"Login","Login"
"Login & Register","Login & Register"
"Login and password are required.","Login and password are required."
"MOTO does not support two-factor authentication.","MOTO does not support two-factor authentication."
"Mail order/telephone order authorization","Mail order/telephone order authorization"
"Male","Male"
"Manage Stores","Manage Stores"
"Manage POWERPAY Aliases","Manage POWERPAY Aliases"
"Mandatory","Mandatory"
"MasterCard","MasterCard"
"Maximum Order Total","Maximal order total"
"Merchant ID","Merchant ID"
"Merchant ID (MOTO)","Merchant ID (MOTO)"
"Merchant ID (MOTO) (Test)","Merchant ID (MOTO) (Test)"
"Merchant ID (Test)","Merchant ID (Test)"
"Merchant ID, supplied by MF Group.","Merchant ID, supplied by POWERPAY."
"Message","Message"
"Method Title","Method Title"
"Minimum Order Total","Minimum order total"
"Mode","Mode"
"Month","Month"
"Monthly invoice","Monthly invoice"
"My POWERPAY Aliases","My POWERPAY Aliases"
"New Order","New Ordern"
"New Order Status","New order status"
"New card","New card"
"Next Update Execution Date","Next Update Execution Date"
"No","No"
"No Items","No Items"
"No additional payment information available.","No additional payment information available"
"No authorization method found for payment method !method.","No authorization method found for payment method !method."
"No button returned.","No button returned."
"No refund possible.","No refund possible"
"No response code received.","No response code received."
"No shipping method needed.","No shipping method needed."
"No, use the dropdown.","No, use the dropdown menu."
"Not enough credit.","Not enough credit."
"Only an authorized transaction can be marked as finally declined.","Only authorized transactions can be marked as finally declined."
"Only an uncertain transaction can be marked as finally declined.","Only uncertain transactions can be marked as finally declined."
"Only authorized transaction can be cancelled.","Only authorised transaction can be cancelled."
"Only authorized transaction can be captured.","Only authorised transactions can be captured."
"Only authorized transaction can be refunded.","Only authorised transaction can be refunded."
"Only captured transaction can be refunded.","Only captured transaction can be refunded."
"Only on captured transaction the flag customer refusing to pay can be set.","It is only possible to set a flag for customers refusing to pay on captured transactions."
"Operation Mode","Operation mode"
"Optional","Optional"
"Order #","Order Number"
"Order Grand Total","Order Grand Total"
"Order Information","Order Information"
"Order Prefix","Order Prefix"
"Order Review","Order Review"
"Order Schema","Order Schema"
"Order Summary","Order Summary"
"Order Total","Order total"
"Order cancelled, because the customer was too long in the payment process of POWERPAY.","Order cancelled because the customer was in the payment process of POWERPAY for too long."
"Order was placed using %s","Order was placed using %s"
"Orders","Orders"
"Orders with with payments that are longer pending than the specified timeout in minutes are cancelled automatically.","Orders, with payments that have been pending for longer than the specified time-out in minutes, are cancelled automatically. To use this feature you have to setup the Magento cron."
"Paid Amount","Paid Amount"
"Partial refund not possible. You may retry with the total transaction amount.","Partial refund not possible. The sum is larger than the total captured amount."
"Password","Password"
"Password and password confirmation are not equal.","""Password"" and ""Password Confirmation"" don't match."
"Pay with ____paymentMethodName____","Pay with ____paymentMethodName____"
"Payer First Line","Payer First Line"
"Payer Fourth Line","Payer Fourth Line"
"Payer Second Line","Payer Second Line"
"Payer Third Line","Payer Third Line"
"Payment","Payment"
"Payment ID","Payment ID"
"Payment Id","Payment ID"
"Payment Information","Payment Information"
"Payment Method","Payment Method"
"Payment Period","Payment Period"
"Payment Reference","Payment Reference"
"Payment captured successfully.","Payment captured successfully"
"Payment could not be authorized. Cancelling order. Reason : ","Payment could not be authorised. Order is being cancelled for the reason:"
"Payment delay must be specified in days, @value given.","Payment delay must be specified in days, @value given."
"Payment details can be entered after placing the order","Payment details can be entered after placing the order"
"Payment from Applicable Countries","Payment from applicable countries"
"Payment from Specific Countries","Payment from specific countries"
"Payment from applicable countries","Payment from applicable countries"
"Payment from specific countries","Payment from specific countries"
"Payment is pending at PowerpayCw","Payment is pending with PowerpayCw"
"Payment method description","Payment method description"
"Payment with %s","Payment with %s"
"Payment with POWERPAY","Payment with POWERPAY"
"Payments may be updated periodically (interval in minutes). To use this feature you have to setup magento cron","The state of the payment will be be updated periodically (interval in minutes) via API if activated. To use this feature you have to setup Magento cron."
"Pending","Pending"
"Pending PowerpayCw","Pending PowerpayCw"
"Perform the tests that you receive from POWERPAY.","Perform the tests with the data you received from POWERPAY."
"Phone Number","Phone number"
"Phone number is not valid.","Phone number is not valid."
"Place Order","Place Order"
"Please agree to all the terms and conditions before placing the order.","Please agree to all the terms and conditions before placing the order."
"Please check the entered CVC number.","Please check the entered CVC number."
"Please check the entered credit card number.","Please check the entered credit card number."
"Please enter here the Bank Identifier Code (BIC).","Please enter here the Bank Identifier Code (BIC)."
"Please enter here the CVC code from your card. You find the code on the back of the card.","Please enter the CVC code from your card. You can find the code on the back of the card."
"Please enter here the International Bank Account Number (IBAN).","Please enter here the International Bank Account Number (IBAN)."
"Please enter here the account holder name on the card.","Please enter here the account holder name on the card."
"Please enter here the account holder name.","Please enter here the account holder name."
"Please enter here the account number of your bank account.","Please enter here the International Bank Account Number (IBAN)."
"Please enter here the bank code of your bank.","Please enter here the bank code of your bank"
"Please enter here the card holder name on the card.","Please enter the card holder's name."
"Please enter here the commercial number of your company.","Please enter the commercial number of your company."
"Please enter here the name of the account owner.","Please enter the name of the account owner"
"Please enter here the number on your card.","Please enter your card number."
"Please enter here the sales tax number of your company.","Please enter here the sales tax number of your company."
"Please enter here your social security number.","Please enter your social security number."
"Please enter the location of your bank.","Please enter the location of your bank."
"Please enter the name of your bank.","Please enter the name of your bank."
"Please enter your IBAN number.","Please enter your IBAN number."
"Please enter your issue number, if there is no CVC code on your card.","Please enter your issue number, if there is no CVC code on your card."
"Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.","Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>."
"Please flash or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.', 'http://www.sellxed.com/en/support","Please empty or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.', 'http://www.sellxed.com/en/support"
"Please flush or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>.","Please flush or disable the cache storage and retry. If this did not help, change the authorization method to PaymentPage and <a href=""%s"" target=""_blank"">contact sellxed</a>."
"Please log in below:","Please log in below:"
"Please provide a non-empty phone number.","Please provide a non-empty phone number."
"Please provide a valid swiss phone number.","Please provide a valid swiss phone number."
"Please select","Please select"
"Please select a gender.","Please select a gender."
"Please select a shipping method before sending the order.","Please select a shipping method before sending the order."
"Please select the Bank Identifier Code (BIC) of your bank.","Please select the Bank Identifier Code (BIC) of your bank."
"Please select the day of your birth.","Please select the day of your birth."
"Please select the expiry month on your card.","You have to enter the month of the card expiry"
"Please select the expiry year on your card.","Please select your card's year of expiry."
"Please select the month of your birth.","Please select the month of your birth."
"Please select the the year of your birth.","Please select the the year of your birth."
"Please set the value for '@label' in your configuration.","Please set the value for '@label' in your configuration."
"Please set your company name to Phantasy Consulting to test B2B invoice","Please set your company name to Phantasy Consulting to test B2B invoice"
"Please set your company name to Phantasy Révision SA to test B2B invoice","Please set your company name to Phantasy Révision SA to test B2B invoice"
"Please wait, processing your order...","Please wait, processing your order..."
"Price","Price"
"Product","Product"
"Product Name","Product name"
"Purchase Point","Point of Purchase"
"Qty","Qty"
"Received unknown response code @code.","Received unknown response code @code."
"Recurring Payments","Recurring Payments"
"Reference Line 1","Reference Line 1"
"Reference Line 2","Reference Line 2"
"Refund Amount","Refunded amount"
"Refund ID","Refund ID"
"Refund amount","Refunded amount"
"Refund information","Refund information"
"Refund status","Refund status"
"Refunded Amount","Refunded amount"
"Refunds","Credit Memo"
"Register","Register"
"Register to Create an Account","Register to create an account"
"Register with us for future convenience:","Register with us for future convenience:"
"Release Date","Release Date"
"Response code not ok, @code","Response code not ok, @code"
"Response data @index not given, required.","Response data @index not given, required."
"Response received with code @code","Response received with code @code"
"Review","Review"
"Rounding Adjustment","Rounding Adjustments"
"Row Total","Row Total"
"SEPA Mandate ID","SEPA Mandate ID"
"SKU:","SKU:"
"Sales","Sales"
"Sales Tax Number","Sales Tax Number"
"Save","Save"
"Saved cards:","Saved cards:"
"See order and shipping status","See order and shipping status"
"Select Brand","Select card brand"
"Select Method","Select Method"
"Select the authorization method to use for processing this payment method.","Select the authorisation method to use in order to process this payment method. (Please be aware that the hidden mode for credit cards (if available) requires additional PCI certification requirements. Contact POWERPAY for additional information)."
"Select the date of your birth.","Select your date of birth."
"Select the date on which your card expires.","Select the expiry date of your card."
"Sellxed On Hold","Sellxed On Hold"
"Send Invoice Email","Send Invoice Email"
"Send amounts to POWERPAY in base currency.","Send amounts to POWERPAY in base currency."
"Send amounts to POWERPAY in store's base currency.","Send amounts to POWERPAY in store's base currency."
"Send invoice email","Send invoice email"
"Send the invoice email automatically after invoice creation.","Send the invoice email automatically after invoice creation."
"Set direct capture after order. In this case your authoization will directly be captured on your customer's credit card","With 'direct capturing' the the customer is debited directly after completion of payment. In the case of 'deferred capturing' the amount is debited at a moment defined by you."
"Set the sort order to show the external checkouts.","Set the sort order to show the external checkouts."
"Settlement after order","Settlement after order"
"Setup","Setup"
"Shipping & Handling","Shipping & Handling"
"Shipping Address","Delivery address"
"Shipping Amount","Shipping Amount"
"Shipping Information","Shipping Information"
"Shipping Method","Shipping Method"
"Shipping Methods","Shipping Methods"
"Shipping Refund","Shipping Refund"
"Shop","Shop"
"Short Installation Instructions:","Short Installation Instructions:"
"Should the amount be confirmed / settled automatically after the order (auto settle / direct) or should the amount only be reserved (deferred)?","Should the amount be confirmed / settled automatically after the order (auto settle / direct) or should the amount only be reserved (deferred)?"
"Should the phone number be required for this payment method? If mandatory is selected the customer will be prompted to enter the phone number during the payment process if not provided by the shop system.","Should the phone number be required for this payment method? If mandatory is selected, the customer will be prompted to enter the phone number during the payment process if not provided by the shop system."
"Show","Show"
"Show Account Selection","Show Account Selection"
"Show Further Settings and Information","Show further settings and information"
"Show Image","Show Image"
"Show Payment Id","Show Payment ID"
"Show the payment id","Show the payment ID"
"Show the payment id in the order information","Show the payment ID in the order information."
"Show the payment image","Show the payment image"
"Show the payment image on the checkout page","Show the payment image on the checkout page"
"Sign In","Sign In"
"Social Security Number","Social Security Number"
"Sort Order","Sort Order"
"Status","State"
"Store the payment details","Store the payment details"
"Stored payment details","Stored payment details"
"Submit Order","Submit Order"
"Submitting order information...","Submitting order information..."
"Subtotal","Subtotal"
"Success","Successful"
"Successful","Successful"
"Successful online refund of amount","Successful online refund of amount"
"Successful payment authorization.","Successful payment authorization"
"Successfull online refund of amount","Successful online refund of amount"
"Support","Support"
"Target type @expected expected, @actual received.","Target type @expected expected, @actual was received."
"Tax Amount","Tax Amount"
"Technical issue: This payment methods is not available at the moment.","Technical issue: This payment method is not available at the moment."
"Terminal ID","Terminal ID"
"Terminal ID (MOTO)","Terminal ID (MOTO)"
"Terminal ID (MOTO) (Test)","Terminal ID (MOTO) (Test)"
"Terminal ID (Test)","Terminal ID (Test)"
"Terminal ID, supplied by MF Group.","Terminal ID, supplied by POWERPAY."
"Test Mode","Test Mode"
"Test Transaction","Test Transaction"
"The HMAC key as defined in the live backoffice of POWERPAY. You may need generate the HMAC key. Please take a look at the manual how to do this.","The HMAC key as defined in the live backoffice of POWERPAY. You may need generate the HMAC key. Please take a look at the manual how to do this."
"The SEPA mandate schema does not contain the tag '!tag'. This tag is required.","The SEPA mandate schema does not contain the tag '!tag'. This tag is required."
"The alias could not be found.","The alias could not be found."
"The alias has been deleted.","The alias has been deleted."
"The alias transaction could not be conductet with error code: !code, and reason: !reason:","The alias transaction could not be conducted with error code: !code, and reason: !reason:"
"The amount could not be verified.","The amount could not be verified."
"The amount of !amount is authorized.","The amount of !amount is authorized."
"The amount of !amount is captured.","The amount of !amount is captured."
"The authentication failed. Please check the configured Authentication User and Password.","The authentication failed. Please check the configured Authentication User and Password."
"The authorization method '!method' is not supported.","The authorization method '!method' is not supported."
"The authorization was not successful","Authorization error. Authorization not successful."
"The available balance for the given card number is too small","The available balance for the given card number is too small."
"The brand of the credit card is detected by the card number if hidden authorization is used. If the payment page is used, the user has to select the brand. The allowed credit card brands can be restricted by this setting.","The brand of the credit card is detected by the card number if hidden authorization is used. If the payment page is used, the user has to select the brand. The allowed credit card brands can be restricted by this setting."
"The brand with external name '!key' was not present in the map.","The brand with external name '!key' was not present in the map."
"The brand with key '!key' was not found in the card information map.","The brand with key '!key' was not found in the card information map."
"The cancellation could not be conducted. Error code: !error.","The cancellation could not be conducted. Error code: !error."
"The capture amount (!captureAmount) cannot be greater than the authorized amount (!authorizedAmount).","The capture amount (!captureAmount) cannot be greater than the authorized amount (!authorizedAmount)."
"The capture item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal).","The capture item with SKU '@sku' has a higher amount (@amountItem) than the original item (@amountOriginal)."
"The capture item with SKU '@sku' is not present in the original order.","The capture item with SKU '@sku' is not present in the original order."
"The capturing could not be conducted. Error code: !error.","The capturing could not be conducted. Error code: !error."
"The card brand selection is automatically done depending on the entered card number. If no Javascript is active in the browser, a drop down is shown. In case JavaScript a the brand logos can be shown. Should the brand selection using images?","The card brand selection is automatically done depending on the entered card number. If no Javascript is active in the browser, a drop down is shown. In case JavaScript a the brand logos can be shown should the brand selection using images."
"The configuration has been saved.","The configuration has been saved."
"The controller class '@controller' does not provide any method with annotation 'Customweb_Payment_Endpoint_Annotation_ExtractionMethod' and valid output.","The controller class '@controller' does not provide any method with the annotation 'Customweb_Payment_Endpoint_Annotation_ExtractionMethod' and valid output."
"The customer does not finish the payment with in the timeout.","The customer does not finish the payment with in the timeout."
"The date of birth is required.","The date of birth is required."
"The expected format is '!format' where the 'MM' means the month number and 'YY' the year number.","The expected format is '!format' where the 'MM' means the month in two digits and 'YY' the year in two digits."
"The field containing the value for @index must be filled.","The field containing the value for @index must be filled."
"The gender is required.","The gender is required."
"The given CVC code has the wrong length.","The given CVC code has the wrong length."
"The given card number has an invalid check sum.","The given card number has an invalid check sum."
"The given card number has an invalid length.","The given card number has an invalid length."
"The given hmac key is empty.","No HMAC key entered."
"The given identifier is empty.","The given identifier is empty."
"The given password is empty.","No password entered."
"The given rang is empty. It must be a nummeric value with length of two.","The given rang is empty. It must be a numeric value with length of two."
"The given rang is invalid. It must be a nummeric value with length of two.","The given rang is invalid. It must be a numeric value with length of two."
"The given site number is empty.","The given site number is empty."
"The identifier as defined in the backoffice of POWERPAY.","The identifier as defined in the backoffice of POWERPAY."
"The invoice could not be captured and processed by","The invoice could not be captured and processed by POWERPAY."
"The invoice could not be captured and processed. Reason:","The invoice could not be captured and processed. Reason: !reason"
"The invoice could not be found.","The invoice could not be found."
"The invoice has been changed.","The invoice has been changed."
"The invoice no longer exists.","The invoice no longer exists."
"The layout does not contain the tag '!layout_tag'.","The layout does not contain the tag '!layout_tag'."
"The message received by POWERPAY could not be decoded.","The message received by POWERPAY could not be decoded."
"The mode to use when processing transactions.","The mode used when processing transactions."
"The next update execution date indicates when the next cron is run to update the transaction state.","The next update execution date indicates when the next cron is run to update the transaction state."
"The order is not fully completed. Please contact the store owner.","The payment could not be conducted."
"The password used to authenticate requests, supplied by MF Group.","The password used to authenticate requests, supplied by POWERPAY."
"The payment form will be displayed after submitting the order.","The payment form will be displayed after submitting the order."
"The payment has been accepted.","The payment has been accepted."
"The payment has been denied.","The payment has been denied."
"The payment method !paymentMethodName does not support the currency '!currency'.","The payment method !paymentMethodName does not support the currency '!currency'."
"The payment method !paymentMethodName is not available in your country ('!country').","The payment method !paymentMethodName is not available in your country ('!country')."
"The payment methods is only available for B2B transactions.","The payment methods is only available for B2B transactions."
"The payment methods is only available for B2C transactions.","The payment methods is only available for B2C transactions."
"The payment update has been made.","The payment update has been completed."
"The provided signature does not match with the one calculated.","The provided signature does not match the one calculated."
"The rang (PBX_RANG) as defined in the live backoffice of POWERPAY. It must be nummeric value with length two. In the backoffice it may be displayed as a three char value. You must truncate the first digit.","The rang (PBX_RANG) as defined in the live backoffice of POWERPAY. It must be nummeric value with length two. In the back office it may be displayed as a three char value. You must truncate the first digit."
"The refund item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal).","The refund item with SKU '@sku' has a higher amount (@amountItem) as the original item (@amountOriginal)."
"The refund item with SKU '@sku' is not present in the original order.","The refund item with SKU '@sku' is not present in the original order."
"The refunding could not be conducted. Error code: !error.","The refund could not be conducted. Error code: !error."
"The requested action (msgnum: @msgnum) could not be processed, POWERPAY responded with: @code","The requested action (msgnum: @msgnum) could not be processed, POWERPAY responded with: @code"
"The requested action could not be processed, POWERPAY responded with: @code","The requested action could not be processed, POWERPAY responded with: @code"
"The requested action could not be processed: @code","The requested action could not be processed: @code"
"The service responded with: @string","The service responded with: @string"
"The shipping and billing addresses must be equal","The shipping and billing addresses must be identical."
"The shopping cart has been altered!","The shopping cart has been altered!"
"The signature could not be verified.","The signature could not be verified."
"The site id (PBX_RANG) as defined in the backoffice of POWERPAY.","The Site ID (PBX_RANG) as defined in the back office of POWERPAY."
"The total refund amount (!totalRefundedAmount) cannot be greater than the captured amount (!capturedAmount).","The total refund amount (!totalRefundedAmount) cannot be greater than the captured amount (!capturedAmount)."
"The transaction cannot be cancelled online.","The transaction cannot be cancelled online."
"The transaction cannot be captured online.","The transaction cannot be captured online."
"The transaction cannot be refunded online.","The transaction cannot be refunded online."
"The transaction could not be authorized, but did not fail.","The transaction could not be authorized, but did not fail."
"The transaction failed with an unexpected error. Please try again later.","The transaction failed with an unexpected error. Please try again later."
"The transaction failed with error code !code.","The transaction failed with error code: !code"
"The transaction is only executed in the test system. The goods should not be delivered.","The transaction is only executed in the test system. The goods should not be delivered."
"The two-factor authentication failed.","The two-factor authentication failed."
"The uncertain transaction was accepted.","The uncertain transaction was accepted."
"The uncertain transaction was finally declined.","The uncertain transaction was finally declined."
"The username used to authenticate requests, supplied by MF Group.","The username used to authenticate requests, supplied by POWERPAY."
"The whole transaction is cancelled.","The entire transaction has been cancelled."
"There has been a problem during the processing of your payment.","There has been a problem during the processing of your payment."
"There has been a problem during the processing of your payment. Please contact the shop owner to make sure your order was placed successfully.","There has been a problem during the processing of your payment. Please contact the shop owner to make sure your order was placed successfully."
"There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %1","There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %1"
"There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %s","There is a problem with your license. Please contact us (www.sellxed.com/support). Reason: %s"
"There is already a customer registered using this email address. Please login using this email address or enter a different email address to register your account.","There is already a customer registered using this email address. Please login using this email address or enter a different email address to register your account."
"There seems to be a problem with the POWERPAY module.","There seems to be a problem with the POWERPAY module."
"There was an error validating the login and password.","There was an error validating the login and password."
"There was an issue with the payment. Please try again later or choose another payment method.","There was an issue with the payment. Please try again later or choose another payment method."
"This account is not confirmed. <a href=""%s"">Click here</a> to resend confirmation email.","This account is not confirmed. <a href=""%s"">Click here</a> to resend the confirmation email."
"This is a brief installation instruction of the main and most important installation steps. It is important that you strictly follow the check-list. Only by doing so, the secure usage in correspondence with all security regulations can be guaranteed.","This is a brief installation instruction of the most important installation steps. It is important that you strictly follow the check-list. Only by doing so, the secure usage in correspondence with all security regulations can be guaranteed."
"This password is used for recurring payments and for the alias manager as well as for the server authorization. This is the password of the POWERPAY account.","This password is used for recurring payments and for the alias manager as well as for the server authorization. This is the password of the POWERPAY account."
"This setting can be used to activate resp. deactivate the 3D secure check. Please check with PayBox if you are allowed to deactivate the 3D secure check. This setting has only an effect when you use the payment page.","This setting can be used to activate/deactivate the 3D Secure check. Please check with PayBox if you are allowed to deactivate the 3D Secure check. This setting only has an effect if you use the payment page."
"This status is set, when the payment was successfull and it is authorized.","This status is set when the payment was successful and it is authorised."
"This transaction is already closed for further captures.","This transaction is already closed for further captures"
"This transaction is already closed for further refunds.","This transaction has been closed for further refunds."
"This transaction no longer exists.","This transaction no longer exists."
"Timeout for pending payments (min)","Time-out for pending payments (in min)."
"Titel","Title"
"Title","Title"
"Title of the payment method","Name of the payment method"
"Total","Total"
"Total Shipping Charges","Total shipping charges"
"Track order history","Track order history"
"Transaction","Transaction"
"Transaction Authorized","Transaction authorised"
"Transaction Cancelled","Transaction cancelled"
"Transaction Captured","Transaction captured"
"Transaction History","Transaction History"
"Transaction Id","Transaction ID"
"Transaction Paid","Transaction paid"
"Transaction Uncertain","Transaction uncertain"
"Transaction Updates","Transaction Updates"
"Transaction cancelled successfully","Transaction cancelled successfully"
"Transaction captured successfully","Transaction captured successfully"
"Transaction history","Transaction History"
"Transaction was authorized over the deferred authorization mechanism.","Transaction was authorized over the deferred authorization mechanism."
"Transaction was refused by POWERPAY (address).","Transaction was refused by POWERPAY (address)."
"Transaction was refused by POWERPAY (other).","Transaction was refused by POWERPAY (other)."
"Transactions","Transactions"
"Two-factor authentication was cancelled.","Two-factor authentication was cancelled."
"Ultimate Debtor Address Line 1","Ultimate Debtor Address Line 1"
"Ultimate Debtor Address Line 2","Ultimate Debtor Address Line 2"
"Ultimate Debtor Country","Ultimate Debtor Country"
"Ultimate Debtor Name","Ultimate Debtor Name"
"Unable to automatically capture order.","Unable to automatically capture order."
"Unable to save the invoice.","Unable to save the invoice."
"Uncertain Status","Uncertain status"
"Uncertain transactions","Uncertain transactions"
"Unknown branch (filial)","Unknown branch (filial)"
"Unknown card","Unknown card"
"Unknown merchant","Unknown merchant"
"Unknown terminal","Unknown terminal"
"Unless you select this checkbox, the payment will be captured at the PSP and you can not add additional captures.","Unless you select this checkbox, the payment will be captured at the PSP and you can not add additional captures."
"Update","Update"
"Update Qty's","Adjust Quantity"
"Update interval for payments","Update interval for payments"
"Use Base Currency","Use Base Currency"
"Use Default","Use Default"
"Use base currency","Use base currency"
"VISA","VISA"
"Validation","Validation"
"Validation error","Validation error"
"Version","Version"
"View","View"
"Wait On Success Page","Wait On Success Page"
"We cannot get the transaction instance.","We can not establish a transaction."
"We experienced a problem with your sellxed payment extension. For more information, please visit the configuration page of the plugin.","We experienced a problem with your sellxed payment extension. For more information, please visit the configuration page of the POWERPAY plugin."
"When the store is not available (network outage, server failure or any other outage), when the feedback of POWERPAY is sent, then the transaction state is not updated. Hence no order confirmation e-mail is sent and the order is not in the paid state. By activating the transaction update, such transactions can be authorized later over direct link. To use this feature the update service must be activated and the API username and the API password must be set.","When the store is not available (network outage, server failure or any other outage), when the feedback of POWERPAY is sent, then the transaction state is not updated. Hence no order confirmation e-mail is sent and the order is not in the paid state. By activating the transaction update, such transactions can be authorized later over direct link. To use this feature the update service must be activated and the API username and the API password must be set."
"When the validation should be processed. We recommend to set this to 'Before selecting payment method'. In this case the address will be validated before the payment method is shown.","This setting determines, when the validation should be processed. We recommend to set this to 'Before selecting payment method'. In this case the address will be validated before the payment method is shown."
"When using an external checkout, the customer can either be asked to chose an option to authenticate (as guest, register or login) or he can always be checked out as guest. For the second option to work, guest checkout has to be enabled in magento.","When using an external checkout, the customer can either be asked to choose an option to authenticate (as guest, register or login) or he can always be checked out as guest. For the second option to work, guest checkout has to be enabled in Magento."
"Year","Year"
"Yes","Yes"
"Yes, use images for the brand selection.","Yes, use images for the brand selection."
"You can allow the customers to manage/delete their aliases in the their account.","You can allow the customers to manage/delete their aliases in their account."
"You can decide on the order status new orders should have after they are processed successfully.","You can specify the order status for new orders that have been processed successfully."
"You can decide on the order status new orders should have that have an uncertain authorization status.","You can specify the order status for new orders that have an uncertain authorisation status."
"You can specify the order status for new orders that have an uncertain authorisation status.","You can specify the order status for new orders that have an uncertain authorisation status."
"You can specify the order status for orders that are captured either directly after the order or manually in the backend.","You can specify the order status for orders that are captured either directly after the order or manually in the back-end."
"You can specify the order status when an order is cancelled.","You can specify the order status for cancelled orders."
"You created the order.","You created the order."
"You have no stored aliases.","You have no stored aliases."
"You have to enter a card number.","You have to enter a card number."
"You have to enter bank code of your bank.","Enter your bank's bank code."
"You have to enter commercial number of you company.","Please enter the commercial number of your company."
"You have to enter either the CVC code or the issue number of your card.","You have to enter either the CVC code or the issue number of your card."
"You have to enter sales tax number of you company.","You have to enter sales tax number of you company."
"You have to enter sales tax number of your company.","Please enter your company's sales tax number."
"You have to enter the BIC.","You have to enter the BIC of your bank."
"You have to enter the CVC code from your card.","Enter the CVC code from your card"
"You have to enter the IBAN.","You have to enter your IBAN number."
"You have to enter the account holder name on the card.","You have to enter the account holder name on the card."
"You have to enter the account holder name.","You have to enter the account holder name."
"You have to enter the card holder name on the card.","You have to enter the card holder name."
"You have to enter the location of your bank.","Please enter the location of your bank."
"You have to enter the name of the account owner.","Please enter the name of the account owner"
"You have to enter the name of your bank.","You have to enter the name of your bank."
"You have to enter your IBAN Number.","You have to enter your IBAN number."
"You have to enter your bank account number.","Please enter here the International Bank Account Number (IBAN)."
"You have to enter your social security number.","Please enter your social security number."
"You may choose one of the cards you paid before on this site.","You may choose from one of the cards previously used on this site."
"You may choose one of your stored payment details.","You may choose one of your stored payment details."
"You must provide your phone number.","You must provide your phone number."
"You will be redirect to the order confirmation page.","You will be redirected to the order confirmation page."
"Your browser does not allow the execution of JavaScript. Please activate JavaScript in your browser and reload this page.","Your browser does not allow the execution of JavaScript. Please activate JavaScript in your browser and reload this page."
"Your checkout session expired.","Your checkout session expired."
"Zip code must be between 4 and 8 characters.","Zip code must be between 4 and 8 characters."
"PowerpayCw Aliases","PowerpayCw Aliases"
"____paymentMethodName____","____paymentMethodName____"
"POWERPAY","POWERPAY"
"POWERPAY Aliases","POWERPAY Aliases"
"POWERPAY Error","POWERPAY Error"
"POWERPAY Parameters","POWERPAY Parameters"
"POWERPAY Refund Items","POWERPAY Refund Items"
"POWERPAY Section","POWERPAY Section"
"POWERPAY Transactions","POWERPAY Transactions"
"POWERPAY: Alias Management","POWERPAY: Alias Management"
"customweb ltd","customweb ltd"
"no","No"
"or","or"
"yes","Yes"
