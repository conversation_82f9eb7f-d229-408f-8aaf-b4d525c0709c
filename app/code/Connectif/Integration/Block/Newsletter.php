<?php

namespace Connectif\Integration\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Escaper;
use Magento\Store\Model\ScopeInterface;
use Connectif\Integration\Helper\ConfigHelper;

class Newsletter extends Template
{
    protected $_escaper;
    protected $_cookieManager;
    protected $_cookieMetadataFactory;
    protected $_sessionManager;
    protected $_configHelper;

    /**
     * Constructor.
     * @param Context $context the context.
     * @param array $data optional data.
     */
    public function __construct(
        Context $context,
        Escaper $escaper,
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory,
        ConfigHelper $configHelper,
        SessionManagerInterface $sessionManager,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->_escaper = $escaper;
        $this->_cookieManager = $cookieManager;
        $this->_cookieMetadataFactory = $cookieMetadataFactory;
        $this->_configHelper = $configHelper;
        $this->_sessionManager = $sessionManager;
    }

    /**
     * Return the subscription email if any
     */
    public function getSubscriptionEmail()
    {
        $subscriptionEmail = $this->_cookieManager->getCookie('connectif_newsletter_subscribed');
        if (!$subscriptionEmail) {
            return '';
        }
        $this->_cookieManager->deleteCookie(
            'connectif_newsletter_subscribed',
            $this->_cookieMetadataFactory
                ->createCookieMetadata()
                ->setPath($this->_sessionManager->getCookiePath())
                ->setDomain($this->_sessionManager->getCookieDomain())
        );
        return $subscriptionEmail;
    }

    public function getHtmlEscaper()
    {
        return $this->_escaper;
    }
}
